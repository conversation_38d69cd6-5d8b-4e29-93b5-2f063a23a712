-- ========================================
-- 在庫管理システム インデックス作成スクリプト
-- ========================================

USE WhouseDB;
GO

-- ========================================
-- 商品マスタ関連インデックス
-- ========================================

-- 商品名での検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_M_PRODUCT_NAME')
BEGIN
    CREATE INDEX IX_M_PRODUCT_NAME ON M_PRODUCT(PRODUCT_NAME);
    PRINT 'インデックス IX_M_PRODUCT_NAME を作成しました。';
END
GO

-- カテゴリでの検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_M_PRODUCT_CATEGORY')
BEGIN
    CREATE INDEX IX_M_PRODUCT_CATEGORY ON M_PRODUCT(CATEGORY_CD);
    PRINT 'インデックス IX_M_PRODUCT_CATEGORY を作成しました。';
END
GO

-- 有効フラグでの検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_M_PRODUCT_ACTIVE')
BEGIN
    CREATE INDEX IX_M_PRODUCT_ACTIVE ON M_PRODUCT(ACTIVE_FLG);
    PRINT 'インデックス IX_M_PRODUCT_ACTIVE を作成しました。';
END
GO

-- ========================================
-- カテゴリマスタ関連インデックス
-- ========================================

-- 表示順でのソート用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_M_CATEGORY_SORT')
BEGIN
    CREATE INDEX IX_M_CATEGORY_SORT ON M_CATEGORY(SORT_ORDER);
    PRINT 'インデックス IX_M_CATEGORY_SORT を作成しました。';
END
GO

-- ========================================
-- ユーザーマスタ関連インデックス
-- ========================================

-- ユーザー名での検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_M_USER_NAME')
BEGIN
    CREATE INDEX IX_M_USER_NAME ON M_USER(USER_NAME);
    PRINT 'インデックス IX_M_USER_NAME を作成しました。';
END
GO

-- 権限での検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_M_USER_ROLE')
BEGIN
    CREATE INDEX IX_M_USER_ROLE ON M_USER(USER_ROLE);
    PRINT 'インデックス IX_M_USER_ROLE を作成しました。';
END
GO

-- ========================================
-- 入庫履歴関連インデックス
-- ========================================

-- 入庫日での検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_T_RECEIPT_DATE')
BEGIN
    CREATE INDEX IX_T_RECEIPT_DATE ON T_RECEIPT(RECEIPT_DATE);
    PRINT 'インデックス IX_T_RECEIPT_DATE を作成しました。';
END
GO

-- 商品コードでの検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_T_RECEIPT_PRODUCT')
BEGIN
    CREATE INDEX IX_T_RECEIPT_PRODUCT ON T_RECEIPT(PRODUCT_CD);
    PRINT 'インデックス IX_T_RECEIPT_PRODUCT を作成しました。';
END
GO

-- 仕入先での検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_T_RECEIPT_SUPPLIER')
BEGIN
    CREATE INDEX IX_T_RECEIPT_SUPPLIER ON T_RECEIPT(SUPPLIER);
    PRINT 'インデックス IX_T_RECEIPT_SUPPLIER を作成しました。';
END
GO

-- 作成日時での検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_T_RECEIPT_CREATE_DATE')
BEGIN
    CREATE INDEX IX_T_RECEIPT_CREATE_DATE ON T_RECEIPT(CREATE_DATE);
    PRINT 'インデックス IX_T_RECEIPT_CREATE_DATE を作成しました。';
END
GO

-- ========================================
-- 出庫履歴関連インデックス
-- ========================================

-- 出庫日での検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_T_ISSUE_DATE')
BEGIN
    CREATE INDEX IX_T_ISSUE_DATE ON T_ISSUE(ISSUE_DATE);
    PRINT 'インデックス IX_T_ISSUE_DATE を作成しました。';
END
GO

-- 商品コードでの検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_T_ISSUE_PRODUCT')
BEGIN
    CREATE INDEX IX_T_ISSUE_PRODUCT ON T_ISSUE(PRODUCT_CD);
    PRINT 'インデックス IX_T_ISSUE_PRODUCT を作成しました。';
END
GO

-- 出庫先での検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_T_ISSUE_TO')
BEGIN
    CREATE INDEX IX_T_ISSUE_TO ON T_ISSUE(ISSUE_TO);
    PRINT 'インデックス IX_T_ISSUE_TO を作成しました。';
END
GO

-- 作成日時での検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_T_ISSUE_CREATE_DATE')
BEGIN
    CREATE INDEX IX_T_ISSUE_CREATE_DATE ON T_ISSUE(CREATE_DATE);
    PRINT 'インデックス IX_T_ISSUE_CREATE_DATE を作成しました。';
END
GO

-- ========================================
-- 在庫テーブル関連インデックス
-- ========================================

-- 最終更新日時での検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_T_STOCK_LAST_UPDATE')
BEGIN
    CREATE INDEX IX_T_STOCK_LAST_UPDATE ON T_STOCK(LAST_UPDATE);
    PRINT 'インデックス IX_T_STOCK_LAST_UPDATE を作成しました。';
END
GO

-- ========================================
-- システムログ関連インデックス
-- ========================================

-- ログ日時での検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_T_SYSTEM_LOG_DATE')
BEGIN
    CREATE INDEX IX_T_SYSTEM_LOG_DATE ON T_SYSTEM_LOG(LOG_DATE);
    PRINT 'インデックス IX_T_SYSTEM_LOG_DATE を作成しました。';
END
GO

-- ユーザーIDでの検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_T_SYSTEM_LOG_USER')
BEGIN
    CREATE INDEX IX_T_SYSTEM_LOG_USER ON T_SYSTEM_LOG(USER_ID);
    PRINT 'インデックス IX_T_SYSTEM_LOG_USER を作成しました。';
END
GO

-- アクション種別での検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_T_SYSTEM_LOG_ACTION')
BEGIN
    CREATE INDEX IX_T_SYSTEM_LOG_ACTION ON T_SYSTEM_LOG(ACTION_TYPE);
    PRINT 'インデックス IX_T_SYSTEM_LOG_ACTION を作成しました。';
END
GO

-- テーブル名での検索用インデックス
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_T_SYSTEM_LOG_TABLE')
BEGIN
    CREATE INDEX IX_T_SYSTEM_LOG_TABLE ON T_SYSTEM_LOG(TABLE_NAME);
    PRINT 'インデックス IX_T_SYSTEM_LOG_TABLE を作成しました。';
END
GO

PRINT '全てのインデックスが正常に作成されました。';
