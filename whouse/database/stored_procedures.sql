-- ========================================
-- 在庫管理システム ストアドプロシージャ作成スクリプト
-- ========================================

USE WhouseDB;
GO

-- ========================================
-- 在庫更新プロシージャ
-- ========================================

-- 入庫処理プロシージャ
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_RECEIVE_STOCK')
    DROP PROCEDURE SP_RECEIVE_STOCK;
GO

CREATE PROCEDURE SP_RECEIVE_STOCK
    @RECEIPT_NO NVARCHAR(20),
    @RECEIPT_DATE DATE,
    @PRODUCT_CD NVARCHAR(20),
    @QUANTITY INT,
    @UNIT_COST DECIMAL(10,2) = NULL,
    @SUPPLIER NVARCHAR(100) = NULL,
    @REMARKS NVARCHAR(500) = NULL,
    @USER_ID NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRANSACTION;
    
    BEGIN TRY
        -- 商品の存在チェック
        IF NOT EXISTS (SELECT 1 FROM M_PRODUCT WHERE PRODUCT_CD = @PRODUCT_CD AND ACTIVE_FLG = 1)
        BEGIN
            RAISERROR('指定された商品コードが存在しません: %s', 16, 1, @PRODUCT_CD);
            RETURN;
        END
        
        -- 入庫履歴の登録
        INSERT INTO T_RECEIPT (RECEIPT_NO, RECEIPT_DATE, PRODUCT_CD, QUANTITY, UNIT_COST, SUPPLIER, REMARKS, CREATE_USER)
        VALUES (@RECEIPT_NO, @RECEIPT_DATE, @PRODUCT_CD, @QUANTITY, @UNIT_COST, @SUPPLIER, @REMARKS, @USER_ID);
        
        -- 在庫テーブルの更新
        IF EXISTS (SELECT 1 FROM T_STOCK WHERE PRODUCT_CD = @PRODUCT_CD)
        BEGIN
            UPDATE T_STOCK 
            SET CURRENT_STOCK = CURRENT_STOCK + @QUANTITY,
                LAST_UPDATE = GETDATE(),
                UPDATE_USER = @USER_ID
            WHERE PRODUCT_CD = @PRODUCT_CD;
        END
        ELSE
        BEGIN
            INSERT INTO T_STOCK (PRODUCT_CD, CURRENT_STOCK, RESERVED_STOCK, UPDATE_USER)
            VALUES (@PRODUCT_CD, @QUANTITY, 0, @USER_ID);
        END
        
        -- システムログの記録
        INSERT INTO T_SYSTEM_LOG (USER_ID, ACTION_TYPE, TABLE_NAME, RECORD_KEY, NEW_VALUE)
        VALUES (@USER_ID, 'RECEIVE', 'T_RECEIPT', @RECEIPT_NO, 
                CONCAT('商品:', @PRODUCT_CD, ' 数量:', @QUANTITY, ' 仕入先:', ISNULL(@SUPPLIER, '')));
        
        COMMIT TRANSACTION;
        
        PRINT '入庫処理が正常に完了しました。';
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO

-- 出庫処理プロシージャ
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_ISSUE_STOCK')
    DROP PROCEDURE SP_ISSUE_STOCK;
GO

CREATE PROCEDURE SP_ISSUE_STOCK
    @ISSUE_NO NVARCHAR(20),
    @ISSUE_DATE DATE,
    @PRODUCT_CD NVARCHAR(20),
    @QUANTITY INT,
    @ISSUE_TO NVARCHAR(100) = NULL,
    @REMARKS NVARCHAR(500) = NULL,
    @USER_ID NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRANSACTION;
    
    BEGIN TRY
        DECLARE @CURRENT_STOCK INT;
        DECLARE @RESERVED_STOCK INT;
        DECLARE @AVAILABLE_STOCK INT;
        
        -- 商品の存在チェック
        IF NOT EXISTS (SELECT 1 FROM M_PRODUCT WHERE PRODUCT_CD = @PRODUCT_CD AND ACTIVE_FLG = 1)
        BEGIN
            RAISERROR('指定された商品コードが存在しません: %s', 16, 1, @PRODUCT_CD);
            RETURN;
        END
        
        -- 在庫数チェック
        SELECT @CURRENT_STOCK = CURRENT_STOCK, @RESERVED_STOCK = RESERVED_STOCK
        FROM T_STOCK 
        WHERE PRODUCT_CD = @PRODUCT_CD;
        
        IF @CURRENT_STOCK IS NULL
        BEGIN
            RAISERROR('指定された商品の在庫データが存在しません: %s', 16, 1, @PRODUCT_CD);
            RETURN;
        END
        
        SET @AVAILABLE_STOCK = @CURRENT_STOCK - @RESERVED_STOCK;
        
        IF @AVAILABLE_STOCK < @QUANTITY
        BEGIN
            RAISERROR('在庫不足です。利用可能在庫: %d, 出庫要求: %d', 16, 1, @AVAILABLE_STOCK, @QUANTITY);
            RETURN;
        END
        
        -- 出庫履歴の登録
        INSERT INTO T_ISSUE (ISSUE_NO, ISSUE_DATE, PRODUCT_CD, QUANTITY, ISSUE_TO, REMARKS, CREATE_USER)
        VALUES (@ISSUE_NO, @ISSUE_DATE, @PRODUCT_CD, @QUANTITY, @ISSUE_TO, @REMARKS, @USER_ID);
        
        -- 在庫テーブルの更新
        UPDATE T_STOCK 
        SET CURRENT_STOCK = CURRENT_STOCK - @QUANTITY,
            LAST_UPDATE = GETDATE(),
            UPDATE_USER = @USER_ID
        WHERE PRODUCT_CD = @PRODUCT_CD;
        
        -- システムログの記録
        INSERT INTO T_SYSTEM_LOG (USER_ID, ACTION_TYPE, TABLE_NAME, RECORD_KEY, NEW_VALUE)
        VALUES (@USER_ID, 'ISSUE', 'T_ISSUE', @ISSUE_NO, 
                CONCAT('商品:', @PRODUCT_CD, ' 数量:', @QUANTITY, ' 出庫先:', ISNULL(@ISSUE_TO, '')));
        
        COMMIT TRANSACTION;
        
        PRINT '出庫処理が正常に完了しました。';
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO

-- ========================================
-- 番号生成プロシージャ
-- ========================================

-- 次の番号を取得するプロシージャ
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GET_NEXT_NUMBER')
    DROP PROCEDURE SP_GET_NEXT_NUMBER;
GO

CREATE PROCEDURE SP_GET_NEXT_NUMBER
    @NUMBER_TYPE NVARCHAR(20),
    @USER_ID NVARCHAR(50),
    @NEXT_NUMBER NVARCHAR(50) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRANSACTION;
    
    BEGIN TRY
        DECLARE @CURRENT_NUMBER INT;
        DECLARE @PREFIX NVARCHAR(10);
        DECLARE @SUFFIX NVARCHAR(10);
        DECLARE @DIGIT_LENGTH INT;
        
        -- 番号管理テーブルから情報を取得
        SELECT @CURRENT_NUMBER = CURRENT_NUMBER,
               @PREFIX = ISNULL(PREFIX, ''),
               @SUFFIX = ISNULL(SUFFIX, ''),
               @DIGIT_LENGTH = DIGIT_LENGTH
        FROM T_NUMBER_CONTROL
        WHERE NUMBER_TYPE = @NUMBER_TYPE;
        
        IF @CURRENT_NUMBER IS NULL
        BEGIN
            RAISERROR('指定された番号種別が存在しません: %s', 16, 1, @NUMBER_TYPE);
            RETURN;
        END
        
        -- 次の番号を計算
        SET @CURRENT_NUMBER = @CURRENT_NUMBER + 1;
        
        -- 番号管理テーブルを更新
        UPDATE T_NUMBER_CONTROL
        SET CURRENT_NUMBER = @CURRENT_NUMBER,
            UPDATE_DATE = GETDATE(),
            UPDATE_USER = @USER_ID
        WHERE NUMBER_TYPE = @NUMBER_TYPE;
        
        -- フォーマットされた番号を生成
        SET @NEXT_NUMBER = @PREFIX + RIGHT('000000000' + CAST(@CURRENT_NUMBER AS NVARCHAR), @DIGIT_LENGTH) + @SUFFIX;
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO

-- ========================================
-- レポート用プロシージャ
-- ========================================

-- 在庫不足商品取得プロシージャ
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GET_LOW_STOCK_PRODUCTS')
    DROP PROCEDURE SP_GET_LOW_STOCK_PRODUCTS;
GO

CREATE PROCEDURE SP_GET_LOW_STOCK_PRODUCTS
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        p.PRODUCT_CD,
        p.PRODUCT_NAME,
        c.CATEGORY_NAME,
        s.CURRENT_STOCK,
        s.RESERVED_STOCK,
        s.AVAILABLE_STOCK,
        p.MIN_STOCK,
        p.UNIT,
        s.LAST_UPDATE
    FROM M_PRODUCT p
    INNER JOIN M_CATEGORY c ON p.CATEGORY_CD = c.CATEGORY_CD
    INNER JOIN T_STOCK s ON p.PRODUCT_CD = s.PRODUCT_CD
    WHERE p.ACTIVE_FLG = 1
      AND s.AVAILABLE_STOCK <= p.MIN_STOCK
    ORDER BY (s.AVAILABLE_STOCK - p.MIN_STOCK), p.PRODUCT_NAME;
END
GO

PRINT '全てのストアドプロシージャが正常に作成されました。';
