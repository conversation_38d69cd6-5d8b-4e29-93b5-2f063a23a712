-- ========================================
-- 在庫管理システム サンプルデータ投入スクリプト
-- ========================================

USE WhouseDB;
GO

-- ========================================
-- サンプル商品データ投入
-- ========================================

-- 食品カテゴリの商品
INSERT INTO M_PRODUCT (PRODUCT_CD, PRODUCT_NAME, CATEGORY_CD, UNIT_PRICE, MIN_STOCK, UNIT, DESCRIPTION, CREATE_USER, UPDATE_USER)
VALUES 
    ('P000001', 'りんご', 'CAT001', 150.00, 50, '個', '青森県産りんご', 'SYSTEM', 'SYSTEM'),
    ('P000002', 'バナナ', 'CAT001', 100.00, 30, '本', 'フィリピン産バナナ', 'SYSTEM', 'SYSTEM'),
    ('P000003', '牛乳', 'CAT001', 200.00, 20, '本', '1000ml 低温殺菌牛乳', 'SYSTEM', 'SYSTEM'),
    ('P000004', '食パン', 'CAT001', 180.00, 15, '斤', '6枚切り食パン', 'SYSTEM', 'SYSTEM'),
    ('P000005', '卵', 'CAT001', 250.00, 25, 'パック', '10個入り新鮮卵', 'SYSTEM', 'SYSTEM');

-- 飲料カテゴリの商品
INSERT INTO M_PRODUCT (PRODUCT_CD, PRODUCT_NAME, CATEGORY_CD, UNIT_PRICE, MIN_STOCK, UNIT, DESCRIPTION, CREATE_USER, UPDATE_USER)
VALUES 
    ('P000006', 'コーラ', 'CAT002', 120.00, 40, '本', '500ml ペットボトル', 'SYSTEM', 'SYSTEM'),
    ('P000007', 'オレンジジュース', 'CAT002', 150.00, 30, '本', '500ml 100%果汁', 'SYSTEM', 'SYSTEM'),
    ('P000008', '緑茶', 'CAT002', 100.00, 50, '本', '500ml ペットボトル', 'SYSTEM', 'SYSTEM'),
    ('P000009', 'コーヒー', 'CAT002', 130.00, 35, '本', '缶コーヒー 185ml', 'SYSTEM', 'SYSTEM'),
    ('P000010', 'ミネラルウォーター', 'CAT002', 80.00, 60, '本', '500ml 天然水', 'SYSTEM', 'SYSTEM');

-- 日用品カテゴリの商品
INSERT INTO M_PRODUCT (PRODUCT_CD, PRODUCT_NAME, CATEGORY_CD, UNIT_PRICE, MIN_STOCK, UNIT, DESCRIPTION, CREATE_USER, UPDATE_USER)
VALUES 
    ('P000011', 'ティッシュペーパー', 'CAT003', 300.00, 20, '箱', '5箱パック', 'SYSTEM', 'SYSTEM'),
    ('P000012', 'トイレットペーパー', 'CAT003', 400.00, 15, 'パック', '12ロール', 'SYSTEM', 'SYSTEM'),
    ('P000013', '洗剤', 'CAT003', 250.00, 10, '本', '食器用洗剤 500ml', 'SYSTEM', 'SYSTEM'),
    ('P000014', 'シャンプー', 'CAT003', 500.00, 8, '本', '400ml ボトル', 'SYSTEM', 'SYSTEM'),
    ('P000015', '歯ブラシ', 'CAT003', 150.00, 25, '本', '普通の硬さ', 'SYSTEM', 'SYSTEM');

-- 文房具カテゴリの商品
INSERT INTO M_PRODUCT (PRODUCT_CD, PRODUCT_NAME, CATEGORY_CD, UNIT_PRICE, MIN_STOCK, UNIT, DESCRIPTION, CREATE_USER, UPDATE_USER)
VALUES 
    ('P000016', 'ボールペン', 'CAT004', 100.00, 50, '本', '黒インク 0.7mm', 'SYSTEM', 'SYSTEM'),
    ('P000017', 'ノート', 'CAT004', 200.00, 30, '冊', 'A4 横罫 30枚', 'SYSTEM', 'SYSTEM'),
    ('P000018', '消しゴム', 'CAT004', 80.00, 40, '個', '白色 プラスチック', 'SYSTEM', 'SYSTEM'),
    ('P000019', '定規', 'CAT004', 120.00, 20, '本', '30cm 透明', 'SYSTEM', 'SYSTEM'),
    ('P000020', 'ホッチキス', 'CAT004', 800.00, 5, '個', '中型 針付き', 'SYSTEM', 'SYSTEM');

-- ========================================
-- サンプル在庫データ投入
-- ========================================

-- 各商品の初期在庫を設定
INSERT INTO T_STOCK (PRODUCT_CD, CURRENT_STOCK, RESERVED_STOCK, UPDATE_USER)
VALUES 
    ('P000001', 100, 0, 'SYSTEM'),
    ('P000002', 80, 5, 'SYSTEM'),
    ('P000003', 50, 0, 'SYSTEM'),
    ('P000004', 30, 2, 'SYSTEM'),
    ('P000005', 60, 0, 'SYSTEM'),
    ('P000006', 120, 10, 'SYSTEM'),
    ('P000007', 90, 0, 'SYSTEM'),
    ('P000008', 150, 0, 'SYSTEM'),
    ('P000009', 80, 5, 'SYSTEM'),
    ('P000010', 200, 0, 'SYSTEM'),
    ('P000011', 40, 0, 'SYSTEM'),
    ('P000012', 25, 0, 'SYSTEM'),
    ('P000013', 20, 0, 'SYSTEM'),
    ('P000014', 15, 0, 'SYSTEM'),
    ('P000015', 50, 0, 'SYSTEM'),
    ('P000016', 100, 0, 'SYSTEM'),
    ('P000017', 60, 0, 'SYSTEM'),
    ('P000018', 80, 0, 'SYSTEM'),
    ('P000019', 30, 0, 'SYSTEM'),
    ('P000020', 10, 0, 'SYSTEM');

-- ========================================
-- サンプル入庫履歴データ投入
-- ========================================

-- 過去1ヶ月の入庫履歴
INSERT INTO T_RECEIPT (RECEIPT_NO, RECEIPT_DATE, PRODUCT_CD, QUANTITY, UNIT_COST, SUPPLIER, REMARKS, CREATE_USER)
VALUES 
    ('R00000001', '2024-08-01', 'P000001', 50, 120.00, '青森農協', '品質良好', 'admin'),
    ('R00000002', '2024-08-01', 'P000002', 30, 80.00, 'フルーツ商事', '', 'admin'),
    ('R00000003', '2024-08-05', 'P000003', 20, 180.00, '乳業メーカー', '賞味期限注意', 'admin'),
    ('R00000004', '2024-08-05', 'P000006', 60, 100.00, '飲料卸売', '', 'admin'),
    ('R00000005', '2024-08-10', 'P000011', 20, 250.00, '日用品商事', '', 'admin'),
    ('R00000006', '2024-08-15', 'P000016', 50, 80.00, '文具メーカー', 'まとめ買い割引', 'admin'),
    ('R00000007', '2024-08-20', 'P000001', 50, 125.00, '青森農協', '', 'admin'),
    ('R00000008', '2024-08-25', 'P000008', 100, 85.00, '茶葉商事', '夏季限定価格', 'admin');

-- ========================================
-- サンプル出庫履歴データ投入
-- ========================================

-- 過去1ヶ月の出庫履歴
INSERT INTO T_ISSUE (ISSUE_NO, ISSUE_DATE, PRODUCT_CD, QUANTITY, ISSUE_TO, REMARKS, CREATE_USER)
VALUES 
    ('I00000001', '2024-08-02', 'P000001', 20, '店舗A', '通常販売', 'user01'),
    ('I00000002', '2024-08-03', 'P000002', 15, '店舗B', '', 'user01'),
    ('I00000003', '2024-08-06', 'P000006', 30, '店舗A', '夏季需要増', 'user01'),
    ('I00000004', '2024-08-08', 'P000003', 10, '店舗C', '', 'user01'),
    ('I00000005', '2024-08-12', 'P000011', 5, '店舗A', '', 'user01'),
    ('I00000006', '2024-08-16', 'P000016', 25, '店舗B', '新学期需要', 'user01'),
    ('I00000007', '2024-08-18', 'P000001', 30, '店舗C', '', 'user01'),
    ('I00000008', '2024-08-22', 'P000008', 50, '店舗A', '', 'user01'),
    ('I00000009', '2024-08-26', 'P000009', 20, '店舗B', '', 'user01'),
    ('I00000010', '2024-08-28', 'P000002', 10, '店舗A', '', 'user01');

-- 番号管理テーブルの更新
UPDATE T_NUMBER_CONTROL SET CURRENT_NUMBER = 8 WHERE NUMBER_TYPE = 'RECEIPT';
UPDATE T_NUMBER_CONTROL SET CURRENT_NUMBER = 10 WHERE NUMBER_TYPE = 'ISSUE';
UPDATE T_NUMBER_CONTROL SET CURRENT_NUMBER = 20 WHERE NUMBER_TYPE = 'PRODUCT';

PRINT 'サンプルデータの投入が完了しました。';
PRINT '商品: 20件, 在庫: 20件, 入庫履歴: 8件, 出庫履歴: 10件';
