-- ========================================
-- 在庫管理システム データベース作成スクリプト
-- ========================================
-- データベース作成
IF NOT EXISTS (
    SELECT
        name
    FROM
        sys.databases
    WHERE
        name = 'WhouseDB'
) BEGIN CREATE DATABASE WhouseDB COLLATE Japanese_CI_AS;

END GO USE WhouseDB;

GO
-- ========================================
-- マスタテーブル作成
-- ========================================
-- カテゴリマスタ
IF NOT EXISTS (
    SELECT
        *
    FROM
        sysobjects
    WHERE
        name = 'M_CATEGORY'
        AND xtype = 'U'
) BEGIN
CREATE TABLE
    M_CATEGORY (
        CATEGORY_CD NVARCHAR (10) NOT NULL, -- カテゴリコード
        CATEGORY_NAME NVARCHAR (50) NOT NULL, -- カテゴリ名
        SORT_ORDER INT NOT NULL, -- 表示順
        ACTIVE_FLG BIT NOT NULL DEFAULT 1, -- 有効フラグ
        CREATE_DATE DATETIME NOT NULL DEFAULT GETDATE (), -- 作成日時
        CREATE_USER NVARCHAR (50) NOT NULL, -- 作成者
        UPDATE_DATE DATETIME NOT NULL DEFAULT GETDATE (), -- 更新日時
        UPDATE_USER NVARCHAR (50) NOT NULL, -- 更新者
        CONSTRAINT PK_M_CATEGORY PRIMARY KEY (CATEGORY_CD)
    );

PRINT 'テーブル M_CATEGORY を作成しました。';

END GO
-- 商品マスタ
IF NOT EXISTS (
    SELECT
        *
    FROM
        sysobjects
    WHERE
        name = 'M_PRODUCT'
        AND xtype = 'U'
) BEGIN
CREATE TABLE
    M_PRODUCT (
        PRODUCT_CD NVARCHAR (20) NOT NULL, -- 商品コード
        PRODUCT_NAME NVARCHAR (100) NOT NULL, -- 商品名
        CATEGORY_CD NVARCHAR (10) NOT NULL, -- カテゴリコード
        UNIT_PRICE DECIMAL(10, 2) NOT NULL DEFAULT 0, -- 単価
        MIN_STOCK INT NOT NULL DEFAULT 0, -- 最小在庫数
        UNIT NVARCHAR (10) NOT NULL DEFAULT '個', -- 単位
        DESCRIPTION NVARCHAR (500) NULL, -- 説明
        ACTIVE_FLG BIT NOT NULL DEFAULT 1, -- 有効フラグ
        CREATE_DATE DATETIME NOT NULL DEFAULT GETDATE (), -- 作成日時
        CREATE_USER NVARCHAR (50) NOT NULL, -- 作成者
        UPDATE_DATE DATETIME NOT NULL DEFAULT GETDATE (), -- 更新日時
        UPDATE_USER NVARCHAR (50) NOT NULL, -- 更新者
        CONSTRAINT PK_M_PRODUCT PRIMARY KEY (PRODUCT_CD),
        CONSTRAINT FK_M_PRODUCT_CATEGORY FOREIGN KEY (CATEGORY_CD) REFERENCES M_CATEGORY (CATEGORY_CD)
    );

PRINT 'テーブル M_PRODUCT を作成しました。';

END GO
-- ユーザーマスタ
IF NOT EXISTS (
    SELECT
        *
    FROM
        sysobjects
    WHERE
        name = 'M_USER'
        AND xtype = 'U'
) BEGIN
CREATE TABLE
    M_USER (
        USER_ID NVARCHAR (50) NOT NULL, -- ユーザーID
        USER_NAME NVARCHAR (100) NOT NULL, -- ユーザー名
        PASSWORD NVARCHAR (255) NOT NULL, -- パスワード（ハッシュ化）
        EMAIL NVARCHAR (255) NULL, -- メールアドレス
        USER_ROLE NVARCHAR (20) NOT NULL DEFAULT 'USER', -- ユーザー権限（ADMIN/USER）
        ACTIVE_FLG BIT NOT NULL DEFAULT 1, -- 有効フラグ
        LAST_LOGIN DATETIME NULL, -- 最終ログイン日時
        CREATE_DATE DATETIME NOT NULL DEFAULT GETDATE (), -- 作成日時
        CREATE_USER NVARCHAR (50) NOT NULL, -- 作成者
        UPDATE_DATE DATETIME NOT NULL DEFAULT GETDATE (), -- 更新日時
        UPDATE_USER NVARCHAR (50) NOT NULL, -- 更新者
        CONSTRAINT PK_M_USER PRIMARY KEY (USER_ID)
    );

PRINT 'テーブル M_USER を作成しました。';

END GO
-- ========================================
-- トランザクションテーブル作成
-- ========================================
-- 在庫テーブル
IF NOT EXISTS (
    SELECT
        *
    FROM
        sysobjects
    WHERE
        name = 'T_STOCK'
        AND xtype = 'U'
) BEGIN
CREATE TABLE
    T_STOCK (
        PRODUCT_CD NVARCHAR (20) NOT NULL, -- 商品コード
        CURRENT_STOCK INT NOT NULL DEFAULT 0, -- 現在庫数
        RESERVED_STOCK INT NOT NULL DEFAULT 0, -- 引当済在庫数
        AVAILABLE_STOCK AS (CURRENT_STOCK - RESERVED_STOCK), -- 利用可能在庫数（計算列）
        LAST_UPDATE DATETIME NOT NULL DEFAULT GETDATE (), -- 最終更新日時
        UPDATE_USER NVARCHAR (50) NOT NULL, -- 更新者
        CONSTRAINT PK_T_STOCK PRIMARY KEY (PRODUCT_CD),
        CONSTRAINT FK_T_STOCK_PRODUCT FOREIGN KEY (PRODUCT_CD) REFERENCES M_PRODUCT (PRODUCT_CD)
    );

PRINT 'テーブル T_STOCK を作成しました。';

END GO
-- 入庫履歴
IF NOT EXISTS (
    SELECT
        *
    FROM
        sysobjects
    WHERE
        name = 'T_RECEIPT'
        AND xtype = 'U'
) BEGIN
CREATE TABLE
    T_RECEIPT (
        RECEIPT_NO NVARCHAR (20) NOT NULL, -- 入庫番号
        RECEIPT_DATE DATE NOT NULL, -- 入庫日
        PRODUCT_CD NVARCHAR (20) NOT NULL, -- 商品コード
        QUANTITY INT NOT NULL, -- 入庫数量
        UNIT_COST DECIMAL(10, 2) NULL, -- 仕入単価
        SUPPLIER NVARCHAR (100) NULL, -- 仕入先
        REMARKS NVARCHAR (500) NULL, -- 備考
        CREATE_DATE DATETIME NOT NULL DEFAULT GETDATE (), -- 作成日時
        CREATE_USER NVARCHAR (50) NOT NULL, -- 作成者
        CONSTRAINT PK_T_RECEIPT PRIMARY KEY (RECEIPT_NO),
        CONSTRAINT FK_T_RECEIPT_PRODUCT FOREIGN KEY (PRODUCT_CD) REFERENCES M_PRODUCT (PRODUCT_CD),
        CONSTRAINT CK_T_RECEIPT_QUANTITY CHECK (QUANTITY > 0)
    );

PRINT 'テーブル T_RECEIPT を作成しました。';

END GO
-- 出庫履歴
IF NOT EXISTS (
    SELECT
        *
    FROM
        sysobjects
    WHERE
        name = 'T_ISSUE'
        AND xtype = 'U'
) BEGIN
CREATE TABLE
    T_ISSUE (
        ISSUE_NO NVARCHAR (20) NOT NULL, -- 出庫番号
        ISSUE_DATE DATE NOT NULL, -- 出庫日
        PRODUCT_CD NVARCHAR (20) NOT NULL, -- 商品コード
        QUANTITY INT NOT NULL, -- 出庫数量
        ISSUE_TO NVARCHAR (100) NULL, -- 出庫先
        REMARKS NVARCHAR (500) NULL, -- 備考
        CREATE_DATE DATETIME NOT NULL DEFAULT GETDATE (), -- 作成日時
        CREATE_USER NVARCHAR (50) NOT NULL, -- 作成者
        CONSTRAINT PK_T_ISSUE PRIMARY KEY (ISSUE_NO),
        CONSTRAINT FK_T_ISSUE_PRODUCT FOREIGN KEY (PRODUCT_CD) REFERENCES M_PRODUCT (PRODUCT_CD),
        CONSTRAINT CK_T_ISSUE_QUANTITY CHECK (QUANTITY > 0)
    );

PRINT 'テーブル T_ISSUE を作成しました。';

END GO
-- ========================================
-- システムテーブル作成
-- ========================================
-- システムログ
IF NOT EXISTS (
    SELECT
        *
    FROM
        sysobjects
    WHERE
        name = 'T_SYSTEM_LOG'
        AND xtype = 'U'
) BEGIN
CREATE TABLE
    T_SYSTEM_LOG (
        LOG_ID BIGINT IDENTITY (1, 1) NOT NULL, -- ログID
        LOG_DATE DATETIME NOT NULL DEFAULT GETDATE (), -- ログ日時
        USER_ID NVARCHAR (50) NULL, -- ユーザーID
        ACTION_TYPE NVARCHAR (50) NOT NULL, -- アクション種別
        TABLE_NAME NVARCHAR (50) NULL, -- テーブル名
        RECORD_KEY NVARCHAR (100) NULL, -- レコードキー
        OLD_VALUE NVARCHAR (MAX) NULL, -- 変更前値
        NEW_VALUE NVARCHAR (MAX) NULL, -- 変更後値
        IP_ADDRESS NVARCHAR (50) NULL, -- IPアドレス
        USER_AGENT NVARCHAR (500) NULL, -- ユーザーエージェント
        CONSTRAINT PK_T_SYSTEM_LOG PRIMARY KEY (LOG_ID)
    );

PRINT 'テーブル T_SYSTEM_LOG を作成しました。';

END GO
-- 番号管理テーブル
IF NOT EXISTS (
    SELECT
        *
    FROM
        sysobjects
    WHERE
        name = 'T_NUMBER_CONTROL'
        AND xtype = 'U'
) BEGIN
CREATE TABLE
    T_NUMBER_CONTROL (
        NUMBER_TYPE NVARCHAR (20) NOT NULL, -- 番号種別
        CURRENT_NUMBER INT NOT NULL DEFAULT 0, -- 現在番号
        PREFIX NVARCHAR (10) NULL, -- プレフィックス
        SUFFIX NVARCHAR (10) NULL, -- サフィックス
        DIGIT_LENGTH INT NOT NULL DEFAULT 6, -- 桁数
        UPDATE_DATE DATETIME NOT NULL DEFAULT GETDATE (), -- 更新日時
        UPDATE_USER NVARCHAR (50) NOT NULL, -- 更新者
        CONSTRAINT PK_T_NUMBER_CONTROL PRIMARY KEY (NUMBER_TYPE)
    );

PRINT 'テーブル T_NUMBER_CONTROL を作成しました。';

END GO
-- ========================================
-- 初期データ投入
-- ========================================
-- 番号管理初期データ
INSERT INTO
    T_NUMBER_CONTROL (
        NUMBER_TYPE,
        CURRENT_NUMBER,
        PREFIX,
        DIGIT_LENGTH,
        UPDATE_USER
    )
VALUES
    ('RECEIPT', 0, 'R', 8, 'SYSTEM'),
    ('ISSUE', 0, 'I', 8, 'SYSTEM'),
    ('PRODUCT', 0, 'P', 6, 'SYSTEM');

-- デフォルトユーザー作成（パスワード: admin123）
INSERT INTO
    M_USER (
        USER_ID,
        USER_NAME,
        PASSWORD,
        USER_ROLE,
        CREATE_USER,
        UPDATE_USER
    )
VALUES
    (
        'admin',
        '管理者',
        'admin123',
        'ADMIN',
        'SYSTEM',
        'SYSTEM'
    ),
    (
        'user01',
        '一般ユーザー01',
        'user123',
        'USER',
        'SYSTEM',
        'SYSTEM'
    );

-- デフォルトカテゴリ作成
INSERT INTO
    M_CATEGORY (
        CATEGORY_CD,
        CATEGORY_NAME,
        SORT_ORDER,
        CREATE_USER,
        UPDATE_USER
    )
VALUES
    ('CAT001', '食品', 1, 'SYSTEM', 'SYSTEM'),
    ('CAT002', '飲料', 2, 'SYSTEM', 'SYSTEM'),
    ('CAT003', '日用品', 3, 'SYSTEM', 'SYSTEM'),
    ('CAT004', '文房具', 4, 'SYSTEM', 'SYSTEM'),
    ('CAT005', 'その他', 99, 'SYSTEM', 'SYSTEM');

PRINT '初期データの投入が完了しました。';

PRINT '全てのテーブルが正常に作成されました。';