Imports System.Data
Imports Whouse.Application.Logic.Database.Stock
Imports Whouse.Application.Logic.Database.Transaction
Imports Whouse.Application.Logic.Database.Product

Namespace Report

    '''====================================================================================
    ''' <summary>レポート機能ビジネスロジッククラス</summary>
    '''====================================================================================
    Public Class ReportLogic
        Inherits Dbs.Asphalt.Database.BaseClass.LogicBase

        Private stockDb As StockDatabase
        Private transactionDb As TransactionDatabase
        Private productDb As ProductDatabase

        '''====================================================================================
        ''' <summary>コンストラクタ</summary>
        ''' <param name="security">セキュリティオブジェクト</param>
        '''====================================================================================
        Public Sub New(security As Dbs.Asphalt.Core.SystemLogic.Security)
            MyBase.New(security)
            stockDb = New StockDatabase()
            transactionDb = New TransactionDatabase()
            productDb = New ProductDatabase()
        End Sub

        '''====================================================================================
        ''' <summary>在庫レポートデータを取得</summary>
        ''' <param name="categoryCode">カテゴリコード（省略可）</param>
        ''' <param name="lowStockOnly">在庫不足のみ（省略可）</param>
        ''' <returns>在庫レポートデータテーブル</returns>
        '''====================================================================================
        Public Function GetStockReport(Optional categoryCode As String = "", Optional lowStockOnly As Boolean = False) As DataTable
            Try
                Return stockDb.Search("", categoryCode, lowStockOnly)
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>入庫履歴レポートデータを取得</summary>
        ''' <param name="dateFrom">開始日</param>
        ''' <param name="dateTo">終了日</param>
        ''' <param name="productCode">商品コード（省略可）</param>
        ''' <param name="supplier">仕入先（省略可）</param>
        ''' <returns>入庫履歴レポートデータテーブル</returns>
        '''====================================================================================
        Public Function GetReceiptReport(dateFrom As Date, dateTo As Date, Optional productCode As String = "", Optional supplier As String = "") As DataTable
            Try
                Return transactionDb.SearchReceipts(dateFrom, dateTo, productCode, supplier)
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>出庫履歴レポートデータを取得</summary>
        ''' <param name="dateFrom">開始日</param>
        ''' <param name="dateTo">終了日</param>
        ''' <param name="productCode">商品コード（省略可）</param>
        ''' <param name="issueTo">出庫先（省略可）</param>
        ''' <returns>出庫履歴レポートデータテーブル</returns>
        '''====================================================================================
        Public Function GetIssueReport(dateFrom As Date, dateTo As Date, Optional productCode As String = "", Optional issueTo As String = "") As DataTable
            Try
                Return transactionDb.SearchIssues(dateFrom, dateTo, productCode, issueTo)
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品別売上レポートデータを取得</summary>
        ''' <param name="dateFrom">開始日</param>
        ''' <param name="dateTo">終了日</param>
        ''' <returns>商品別売上レポートデータテーブル</returns>
        '''====================================================================================
        Public Function GetProductSalesReport(dateFrom As Date, dateTo As Date) As DataTable
            Try
                Return transactionDb.GetTransactionStatisticsByProduct(dateFrom, dateTo)
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>在庫推移レポートデータを取得</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="dateFrom">開始日</param>
        ''' <param name="dateTo">終了日</param>
        ''' <returns>在庫推移レポートデータテーブル</returns>
        '''====================================================================================
        Public Function GetStockMovementReport(productCode As String, dateFrom As Date, dateTo As Date) As DataTable
            Try
                Dim sql As String = "SELECT " &
                                   "CONVERT(DATE, t.TXN_DATE) AS TXN_DATE, " &
                                   "t.TXN_TYPE, " &
                                   "t.TXN_NO, " &
                                   "t.QUANTITY, " &
                                   "t.BALANCE " &
                                   "FROM ( " &
                                   "  SELECT RECEIPT_DATE AS TXN_DATE, '入庫' AS TXN_TYPE, RECEIPT_NO AS TXN_NO, QUANTITY, " &
                                   "         SUM(QUANTITY) OVER (PARTITION BY PRODUCT_CD ORDER BY RECEIPT_DATE, RECEIPT_NO) AS BALANCE " &
                                   "  FROM T_RECEIPT WHERE PRODUCT_CD = @PRODUCT_CD AND RECEIPT_DATE BETWEEN @DATE_FROM AND @DATE_TO " &
                                   "  UNION ALL " &
                                   "  SELECT ISSUE_DATE AS TXN_DATE, '出庫' AS TXN_TYPE, ISSUE_NO AS TXN_NO, -QUANTITY AS QUANTITY, " &
                                   "         -SUM(QUANTITY) OVER (PARTITION BY PRODUCT_CD ORDER BY ISSUE_DATE, ISSUE_NO) AS BALANCE " &
                                   "  FROM T_ISSUE WHERE PRODUCT_CD = @PRODUCT_CD AND ISSUE_DATE BETWEEN @DATE_FROM AND @DATE_TO " &
                                   ") t " &
                                   "ORDER BY t.TXN_DATE, t.TXN_NO"

                Dim parameters() As SqlParameter = {
                    New SqlParameter("@PRODUCT_CD", productCode),
                    New SqlParameter("@DATE_FROM", dateFrom),
                    New SqlParameter("@DATE_TO", dateTo)
                }

                Return stockDb.GetDataTable(sql, parameters)

            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>月次在庫サマリーレポートデータを取得</summary>
        ''' <param name="targetMonth">対象月</param>
        ''' <returns>月次在庫サマリーデータテーブル</returns>
        '''====================================================================================
        Public Function GetMonthlySummaryReport(targetMonth As Date) As DataTable
            Try
                Dim monthStart As Date = New Date(targetMonth.Year, targetMonth.Month, 1)
                Dim monthEnd As Date = monthStart.AddMonths(1).AddDays(-1)

                Dim sql As String = "SELECT " &
                                   "c.CATEGORY_NAME, " &
                                   "COUNT(p.PRODUCT_CD) AS PRODUCT_COUNT, " &
                                   "SUM(ISNULL(r.RECEIPT_QTY, 0)) AS TOTAL_RECEIPT, " &
                                   "SUM(ISNULL(i.ISSUE_QTY, 0)) AS TOTAL_ISSUE, " &
                                   "SUM(s.CURRENT_STOCK) AS CURRENT_STOCK, " &
                                   "SUM(CASE WHEN s.AVAILABLE_STOCK <= p.MIN_STOCK THEN 1 ELSE 0 END) AS LOW_STOCK_COUNT " &
                                   "FROM M_CATEGORY c " &
                                   "INNER JOIN M_PRODUCT p ON c.CATEGORY_CD = p.CATEGORY_CD " &
                                   "LEFT JOIN T_STOCK s ON p.PRODUCT_CD = s.PRODUCT_CD " &
                                   "LEFT JOIN (SELECT PRODUCT_CD, SUM(QUANTITY) AS RECEIPT_QTY FROM T_RECEIPT WHERE RECEIPT_DATE BETWEEN @MONTH_START AND @MONTH_END GROUP BY PRODUCT_CD) r ON p.PRODUCT_CD = r.PRODUCT_CD " &
                                   "LEFT JOIN (SELECT PRODUCT_CD, SUM(QUANTITY) AS ISSUE_QTY FROM T_ISSUE WHERE ISSUE_DATE BETWEEN @MONTH_START AND @MONTH_END GROUP BY PRODUCT_CD) i ON p.PRODUCT_CD = i.PRODUCT_CD " &
                                   "WHERE p.ACTIVE_FLG = 1 " &
                                   "GROUP BY c.CATEGORY_CD, c.CATEGORY_NAME, c.SORT_ORDER " &
                                   "ORDER BY c.SORT_ORDER"

                Dim parameters() As SqlParameter = {
                    New SqlParameter("@MONTH_START", monthStart),
                    New SqlParameter("@MONTH_END", monthEnd)
                }

                Return stockDb.GetDataTable(sql, parameters)

            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>ABC分析レポートデータを取得</summary>
        ''' <param name="dateFrom">開始日</param>
        ''' <param name="dateTo">終了日</param>
        ''' <returns>ABC分析レポートデータテーブル</returns>
        '''====================================================================================
        Public Function GetABCAnalysisReport(dateFrom As Date, dateTo As Date) As DataTable
            Try
                Dim sql As String = "WITH ProductSales AS ( " &
                                   "  SELECT p.PRODUCT_CD, p.PRODUCT_NAME, c.CATEGORY_NAME, " &
                                   "         ISNULL(SUM(i.QUANTITY), 0) AS TOTAL_ISSUE_QTY, " &
                                   "         ISNULL(SUM(i.QUANTITY * p.UNIT_PRICE), 0) AS TOTAL_SALES_AMOUNT " &
                                   "  FROM M_PRODUCT p " &
                                   "  INNER JOIN M_CATEGORY c ON p.CATEGORY_CD = c.CATEGORY_CD " &
                                   "  LEFT JOIN T_ISSUE i ON p.PRODUCT_CD = i.PRODUCT_CD AND i.ISSUE_DATE BETWEEN @DATE_FROM AND @DATE_TO " &
                                   "  WHERE p.ACTIVE_FLG = 1 " &
                                   "  GROUP BY p.PRODUCT_CD, p.PRODUCT_NAME, c.CATEGORY_NAME, p.UNIT_PRICE " &
                                   "), " &
                                   "RankedProducts AS ( " &
                                   "  SELECT *, " &
                                   "         ROW_NUMBER() OVER (ORDER BY TOTAL_SALES_AMOUNT DESC) AS RANK, " &
                                   "         SUM(TOTAL_SALES_AMOUNT) OVER () AS TOTAL_AMOUNT, " &
                                   "         SUM(TOTAL_SALES_AMOUNT) OVER (ORDER BY TOTAL_SALES_AMOUNT DESC) AS CUMULATIVE_AMOUNT " &
                                   "  FROM ProductSales " &
                                   ") " &
                                   "SELECT *, " &
                                   "       CASE " &
                                   "         WHEN CUMULATIVE_AMOUNT <= TOTAL_AMOUNT * 0.8 THEN 'A' " &
                                   "         WHEN CUMULATIVE_AMOUNT <= TOTAL_AMOUNT * 0.95 THEN 'B' " &
                                   "         ELSE 'C' " &
                                   "       END AS ABC_CLASS, " &
                                   "       CAST(CUMULATIVE_AMOUNT * 100.0 / TOTAL_AMOUNT AS DECIMAL(5,2)) AS CUMULATIVE_RATIO " &
                                   "FROM RankedProducts " &
                                   "ORDER BY RANK"

                Dim parameters() As SqlParameter = {
                    New SqlParameter("@DATE_FROM", dateFrom),
                    New SqlParameter("@DATE_TO", dateTo)
                }

                Return stockDb.GetDataTable(sql, parameters)

            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>レポートデータをCSV形式で出力</summary>
        ''' <param name="dataTable">データテーブル</param>
        ''' <param name="fileName">ファイル名</param>
        ''' <returns>CSV文字列</returns>
        '''====================================================================================
        Public Function ExportToCsv(dataTable As DataTable, fileName As String) As String
            Try
                If dataTable Is Nothing OrElse dataTable.Rows.Count = 0 Then
                    LastError = "出力するデータがありません。"
                    Return ""
                End If

                Dim csv As New System.Text.StringBuilder()

                ' ヘッダー行
                Dim headers As String() = dataTable.Columns.Cast(Of DataColumn)().Select(Function(column) column.ColumnName).ToArray()
                csv.AppendLine(String.Join(",", headers))

                ' データ行
                For Each row As DataRow In dataTable.Rows
                    Dim fields As String() = row.ItemArray.Select(Function(field) $"""{field}""").ToArray()
                    csv.AppendLine(String.Join(",", fields))
                Next

                Return csv.ToString()

            Catch ex As Exception
                LastError = ex.Message
                Return ""
            End Try
        End Function

    End Class

End Namespace
