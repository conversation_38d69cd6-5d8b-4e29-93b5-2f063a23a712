Imports System.Data
Imports Whouse.Application.Logic.Database.Product

Namespace Product

    '''====================================================================================
    ''' <summary>商品管理ビジネスロジッククラス</summary>
    '''====================================================================================
    Public Class ProductLogic
        Inherits Dbs.Asphalt.Database.BaseClass.LogicBase

        Private productDb As ProductDatabase
        Private categoryDb As CategoryDatabase

        '''====================================================================================
        ''' <summary>コンストラクタ</summary>
        ''' <param name="security">セキュリティオブジェクト</param>
        '''====================================================================================
        Public Sub New(security As Dbs.Asphalt.Core.SystemLogic.Security)
            MyBase.New(security)
            productDb = New ProductDatabase()
            categoryDb = New CategoryDatabase()
        End Sub

        '''====================================================================================
        ''' <summary>商品一覧を取得</summary>
        ''' <returns>商品データテーブル</returns>
        '''====================================================================================
        Public Function GetProductList() As DataTable
            Try
                Return productDb.SelectAll()
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品を取得</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <returns>商品データ行</returns>
        '''====================================================================================
        Public Function GetProduct(productCode As String) As DataRow
            Try
                If String.IsNullOrEmpty(productCode) Then
                    LastError = "商品コードが指定されていません。"
                    Return Nothing
                End If

                Return productDb.SelectByCode(productCode)
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品を検索</summary>
        ''' <param name="keyword">検索キーワード</param>
        ''' <param name="categoryCode">カテゴリコード</param>
        ''' <returns>商品データテーブル</returns>
        '''====================================================================================
        Public Function SearchProducts(keyword As String, Optional categoryCode As String = "") As DataTable
            Try
                Return productDb.Search(keyword, categoryCode)
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品を登録</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="productName">商品名</param>
        ''' <param name="categoryCode">カテゴリコード</param>
        ''' <param name="unitPrice">単価</param>
        ''' <param name="minStock">最小在庫数</param>
        ''' <param name="unit">単位</param>
        ''' <param name="description">説明</param>
        ''' <returns>処理結果</returns>
        '''====================================================================================
        Public Function RegisterProduct(productCode As String, productName As String, categoryCode As String,
                                       unitPrice As Decimal, minStock As Integer, unit As String,
                                       description As String) As Boolean
            Try
                ' 入力値検証
                If Not ValidateProductInput(productCode, productName, categoryCode, unitPrice, minStock, unit) Then
                    Return False
                End If

                ' 商品コード重複チェック
                If productDb.ExistsProductCode(productCode) Then
                    LastError = "指定された商品コードは既に使用されています。"
                    Return False
                End If

                ' カテゴリ存在チェック
                If categoryDb.SelectByCode(categoryCode) Is Nothing Then
                    LastError = "指定されたカテゴリが存在しません。"
                    Return False
                End If

                ' 商品登録
                Dim result As Boolean = productDb.Insert(productCode, productName, categoryCode, unitPrice, minStock, unit, description, Security.UserID)

                If result Then
                    LastMessage = "商品を登録しました。"
                End If

                Return result

            Catch ex As Exception
                LastError = ex.Message
                Return False
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品を更新</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="productName">商品名</param>
        ''' <param name="categoryCode">カテゴリコード</param>
        ''' <param name="unitPrice">単価</param>
        ''' <param name="minStock">最小在庫数</param>
        ''' <param name="unit">単位</param>
        ''' <param name="description">説明</param>
        ''' <returns>処理結果</returns>
        '''====================================================================================
        Public Function UpdateProduct(productCode As String, productName As String, categoryCode As String,
                                     unitPrice As Decimal, minStock As Integer, unit As String,
                                     description As String) As Boolean
            Try
                ' 入力値検証
                If Not ValidateProductInput(productCode, productName, categoryCode, unitPrice, minStock, unit) Then
                    Return False
                End If

                ' 商品存在チェック
                If productDb.SelectByCode(productCode) Is Nothing Then
                    LastError = "指定された商品が存在しません。"
                    Return False
                End If

                ' カテゴリ存在チェック
                If categoryDb.SelectByCode(categoryCode) Is Nothing Then
                    LastError = "指定されたカテゴリが存在しません。"
                    Return False
                End If

                ' 商品更新
                Dim result As Boolean = productDb.Update(productCode, productName, categoryCode, unitPrice, minStock, unit, description, Security.UserID)

                If result Then
                    LastMessage = "商品を更新しました。"
                End If

                Return result

            Catch ex As Exception
                LastError = ex.Message
                Return False
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品を削除</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <returns>処理結果</returns>
        '''====================================================================================
        Public Function DeleteProduct(productCode As String) As Boolean
            Try
                If String.IsNullOrEmpty(productCode) Then
                    LastError = "商品コードが指定されていません。"
                    Return False
                End If

                ' 商品存在チェック
                If productDb.SelectByCode(productCode) Is Nothing Then
                    LastError = "指定された商品が存在しません。"
                    Return False
                End If

                ' 商品削除（在庫チェックはデータアクセス層で実施）
                Dim result As Boolean = productDb.Delete(productCode, Security.UserID)

                If result Then
                    LastMessage = "商品を削除しました。"
                End If

                Return result

            Catch ex As Exception
                LastError = ex.Message
                Return False
            End Try
        End Function

        '''====================================================================================
        ''' <summary>カテゴリ一覧を取得</summary>
        ''' <returns>カテゴリデータテーブル</returns>
        '''====================================================================================
        Public Function GetCategoryList() As DataTable
            Try
                Return categoryDb.SelectAll()
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>ドロップダウン用カテゴリリストを取得</summary>
        ''' <returns>カテゴリデータテーブル</returns>
        '''====================================================================================
        Public Function GetCategoryDropDownList() As DataTable
            Try
                Return categoryDb.SelectForDropDown()
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>次の商品コードを生成</summary>
        ''' <returns>次の商品コード</returns>
        '''====================================================================================
        Public Function GenerateNextProductCode() As String
            Try
                ' 番号生成ストアドプロシージャを呼び出し
                Dim sql As String = "EXEC SP_GET_NEXT_NUMBER @NUMBER_TYPE, @USER_ID, @NEXT_NUMBER OUTPUT"
                Dim parameters() As SqlParameter = {
                    New SqlParameter("@NUMBER_TYPE", "PRODUCT"),
                    New SqlParameter("@USER_ID", Security.UserID),
                    New SqlParameter("@NEXT_NUMBER", SqlDbType.NVarChar, 50) With {.Direction = ParameterDirection.Output}
                }

                productDb.ExecuteNonQuery(sql, parameters)
                Return parameters(2).Value.ToString()

            Catch ex As Exception
                LastError = "商品コード生成でエラーが発生しました: " & ex.Message
                Return ""
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品入力値検証</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="productName">商品名</param>
        ''' <param name="categoryCode">カテゴリコード</param>
        ''' <param name="unitPrice">単価</param>
        ''' <param name="minStock">最小在庫数</param>
        ''' <param name="unit">単位</param>
        ''' <returns>検証結果</returns>
        '''====================================================================================
        Private Function ValidateProductInput(productCode As String, productName As String, categoryCode As String,
                                             unitPrice As Decimal, minStock As Integer, unit As String) As Boolean
            ' 必須項目チェック
            If String.IsNullOrEmpty(productCode) Then
                LastError = "商品コードを入力してください。"
                Return False
            End If

            If String.IsNullOrEmpty(productName) Then
                LastError = "商品名を入力してください。"
                Return False
            End If

            If String.IsNullOrEmpty(categoryCode) Then
                LastError = "カテゴリを選択してください。"
                Return False
            End If

            If String.IsNullOrEmpty(unit) Then
                LastError = "単位を入力してください。"
                Return False
            End If

            ' 数値範囲チェック
            If unitPrice < 0 Then
                LastError = "単価は0以上で入力してください。"
                Return False
            End If

            If minStock < 0 Then
                LastError = "最小在庫数は0以上で入力してください。"
                Return False
            End If

            ' 文字数チェック
            If productCode.Length > 20 Then
                LastError = "商品コードは20文字以内で入力してください。"
                Return False
            End If

            If productName.Length > 100 Then
                LastError = "商品名は100文字以内で入力してください。"
                Return False
            End If

            If unit.Length > 10 Then
                LastError = "単位は10文字以内で入力してください。"
                Return False
            End If

            Return True
        End Function

    End Class

End Namespace
