Imports System.Data
Imports Whouse.Application.Logic.Database.Stock
Imports Whouse.Application.Logic.Database.Product

Namespace Stock

    '''====================================================================================
    ''' <summary>在庫管理ビジネスロジッククラス</summary>
    '''====================================================================================
    Public Class StockLogic
        Inherits Dbs.Asphalt.Database.BaseClass.LogicBase

        Private stockDb As StockDatabase
        Private categoryDb As CategoryDatabase

        '''====================================================================================
        ''' <summary>コンストラクタ</summary>
        ''' <param name="security">セキュリティオブジェクト</param>
        '''====================================================================================
        Public Sub New(security As Dbs.Asphalt.Core.SystemLogic.Security)
            MyBase.New(security)
            stockDb = New StockDatabase()
            categoryDb = New CategoryDatabase()
        End Sub

        '''====================================================================================
        ''' <summary>在庫一覧を取得</summary>
        ''' <returns>在庫データテーブル</returns>
        '''====================================================================================
        Public Function GetStockList() As DataTable
            Try
                Return stockDb.SelectAll()
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品の現在庫数を取得</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <returns>現在庫数</returns>
        '''====================================================================================
        Public Function GetCurrentStock(productCode As String) As Integer
            Try
                If String.IsNullOrEmpty(productCode) Then
                    LastError = "商品コードが指定されていません。"
                    Return -1
                End If

                Dim stockRow As DataRow = stockDb.SelectByProductCode(productCode)
                If stockRow IsNot Nothing Then
                    Return CInt(stockRow("CURRENT_STOCK"))
                Else
                    Return 0 ' 在庫データが存在しない場合は0を返す
                End If

            Catch ex As Exception
                LastError = ex.Message
                Return -1
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品の利用可能在庫数を取得</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <returns>利用可能在庫数</returns>
        '''====================================================================================
        Public Function GetAvailableStock(productCode As String) As Integer
            Try
                If String.IsNullOrEmpty(productCode) Then
                    LastError = "商品コードが指定されていません。"
                    Return -1
                End If

                Dim stockRow As DataRow = stockDb.SelectByProductCode(productCode)
                If stockRow IsNot Nothing Then
                    Return CInt(stockRow("AVAILABLE_STOCK"))
                Else
                    Return 0 ' 在庫データが存在しない場合は0を返す
                End If

            Catch ex As Exception
                LastError = ex.Message
                Return -1
            End Try
        End Function

        '''====================================================================================
        ''' <summary>在庫を検索</summary>
        ''' <param name="keyword">検索キーワード</param>
        ''' <param name="categoryCode">カテゴリコード</param>
        ''' <param name="lowStockOnly">在庫不足のみ</param>
        ''' <returns>在庫データテーブル</returns>
        '''====================================================================================
        Public Function SearchStock(keyword As String, Optional categoryCode As String = "", Optional lowStockOnly As Boolean = False) As DataTable
            Try
                Return stockDb.Search(keyword, categoryCode, lowStockOnly)
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>在庫不足商品を取得</summary>
        ''' <returns>在庫不足商品データテーブル</returns>
        '''====================================================================================
        Public Function GetLowStockProducts() As DataTable
            Try
                Return stockDb.SelectLowStockProducts()
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>在庫を調整</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="newCurrentStock">新しい現在庫数</param>
        ''' <param name="newReservedStock">新しい引当済在庫数</param>
        ''' <param name="reason">調整理由</param>
        ''' <returns>処理結果</returns>
        '''====================================================================================
        Public Function AdjustStock(productCode As String, newCurrentStock As Integer, newReservedStock As Integer, reason As String) As Boolean
            Try
                ' 入力値検証
                If String.IsNullOrEmpty(productCode) Then
                    LastError = "商品コードが指定されていません。"
                    Return False
                End If

                If newCurrentStock < 0 Then
                    LastError = "現在庫数は0以上で入力してください。"
                    Return False
                End If

                If newReservedStock < 0 Then
                    LastError = "引当済在庫数は0以上で入力してください。"
                    Return False
                End If

                If newReservedStock > newCurrentStock Then
                    LastError = "引当済在庫数は現在庫数以下で入力してください。"
                    Return False
                End If

                ' 在庫データ存在チェック
                If Not stockDb.ExistsStock(productCode) Then
                    ' 在庫データが存在しない場合は初期化
                    If Not stockDb.InitializeStock(productCode, Security.UserID) Then
                        LastError = "在庫データの初期化に失敗しました。"
                        Return False
                    End If
                End If

                ' 在庫調整前の値を取得
                Dim oldStockRow As DataRow = stockDb.SelectByProductCode(productCode)
                Dim oldCurrentStock As Integer = If(oldStockRow IsNot Nothing, CInt(oldStockRow("CURRENT_STOCK")), 0)
                Dim oldReservedStock As Integer = If(oldStockRow IsNot Nothing, CInt(oldStockRow("RESERVED_STOCK")), 0)

                ' 在庫更新
                Dim result As Boolean = stockDb.UpdateStock(productCode, newCurrentStock, newReservedStock, Security.UserID)

                If result Then
                    ' システムログに記録
                    RecordStockAdjustmentLog(productCode, oldCurrentStock, oldReservedStock, newCurrentStock, newReservedStock, reason)
                    LastMessage = "在庫を調整しました。"
                End If

                Return result

            Catch ex As Exception
                LastError = ex.Message
                Return False
            End Try
        End Function

        '''====================================================================================
        ''' <summary>在庫統計情報を取得</summary>
        ''' <returns>在庫統計データテーブル</returns>
        '''====================================================================================
        Public Function GetStockStatistics() As DataTable
            Try
                Return stockDb.GetStockStatistics()
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>カテゴリ別在庫統計を取得</summary>
        ''' <returns>カテゴリ別在庫統計データテーブル</returns>
        '''====================================================================================
        Public Function GetStockStatisticsByCategory() As DataTable
            Try
                Return stockDb.GetStockStatisticsByCategory()
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>ドロップダウン用カテゴリリストを取得</summary>
        ''' <returns>カテゴリデータテーブル</returns>
        '''====================================================================================
        Public Function GetCategoryDropDownList() As DataTable
            Try
                Return categoryDb.SelectForDropDown()
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>在庫アラート件数を取得</summary>
        ''' <returns>在庫アラート件数</returns>
        '''====================================================================================
        Public Function GetLowStockAlertCount() As Integer
            Try
                Dim lowStockDt As DataTable = stockDb.SelectLowStockProducts()
                Return If(lowStockDt IsNot Nothing, lowStockDt.Rows.Count, 0)
            Catch ex As Exception
                LastError = ex.Message
                Return 0
            End Try
        End Function

        '''====================================================================================
        ''' <summary>在庫調整ログを記録</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="oldCurrentStock">調整前現在庫数</param>
        ''' <param name="oldReservedStock">調整前引当済在庫数</param>
        ''' <param name="newCurrentStock">調整後現在庫数</param>
        ''' <param name="newReservedStock">調整後引当済在庫数</param>
        ''' <param name="reason">調整理由</param>
        '''====================================================================================
        Private Sub RecordStockAdjustmentLog(productCode As String, oldCurrentStock As Integer, oldReservedStock As Integer,
                                            newCurrentStock As Integer, newReservedStock As Integer, reason As String)
            Try
                Dim oldValue As String = $"現在庫:{oldCurrentStock}, 引当済:{oldReservedStock}"
                Dim newValue As String = $"現在庫:{newCurrentStock}, 引当済:{newReservedStock}, 理由:{reason}"

                Dim sql As String = "INSERT INTO T_SYSTEM_LOG (USER_ID, ACTION_TYPE, TABLE_NAME, RECORD_KEY, OLD_VALUE, NEW_VALUE) " &
                                   "VALUES (@USER_ID, 'STOCK_ADJUST', 'T_STOCK', @PRODUCT_CD, @OLD_VALUE, @NEW_VALUE)"

                Dim parameters() As SqlParameter = {
                    New SqlParameter("@USER_ID", Security.UserID),
                    New SqlParameter("@PRODUCT_CD", productCode),
                    New SqlParameter("@OLD_VALUE", oldValue),
                    New SqlParameter("@NEW_VALUE", newValue)
                }

                stockDb.ExecuteNonQuery(sql, parameters)

            Catch ex As Exception
                ' ログ記録エラーは無視（在庫調整処理は継続）
            End Try
        End Sub

    End Class

End Namespace
