Imports System.Data
Imports Whouse.Application.Logic.Database.Stock
Imports Whouse.Application.Logic.Database.Transaction
Imports Whouse.Application.Logic.Database.Product

Namespace Common

    '''====================================================================================
    ''' <summary>ダッシュボード機能ビジネスロジッククラス</summary>
    '''====================================================================================
    Public Class DashboardLogic
        Inherits Dbs.Asphalt.Database.BaseClass.LogicBase

        Private stockDb As StockDatabase
        Private transactionDb As TransactionDatabase
        Private productDb As ProductDatabase

        '''====================================================================================
        ''' <summary>コンストラクタ</summary>
        ''' <param name="security">セキュリティオブジェクト</param>
        '''====================================================================================
        Public Sub New(security As Dbs.Asphalt.Core.SystemLogic.Security)
            MyBase.New(security)
            stockDb = New StockDatabase()
            transactionDb = New TransactionDatabase()
            productDb = New ProductDatabase()
        End Sub

        '''====================================================================================
        ''' <summary>ダッシュボード統計情報を取得</summary>
        ''' <returns>ダッシュボード統計データテーブル</returns>
        '''====================================================================================
        Public Function GetDashboardStatistics() As DataTable
            Try
                ' 在庫統計
                Dim stockStats As DataTable = stockDb.GetStockStatistics()
                
                ' 本日の入出庫統計
                Dim todayStats As DataTable = transactionDb.GetTransactionStatistics(Date.Today, Date.Today)
                
                ' 今月の入出庫統計
                Dim monthStart As Date = New Date(Date.Today.Year, Date.Today.Month, 1)
                Dim monthEnd As Date = monthStart.AddMonths(1).AddDays(-1)
                Dim monthStats As DataTable = transactionDb.GetTransactionStatistics(monthStart, monthEnd)

                ' 統合データテーブル作成
                Dim dashboardDt As New DataTable()
                dashboardDt.Columns.Add("METRIC_NAME", GetType(String))
                dashboardDt.Columns.Add("METRIC_VALUE", GetType(Object))
                dashboardDt.Columns.Add("METRIC_TYPE", GetType(String))

                ' 在庫統計データ追加
                If stockStats IsNot Nothing AndAlso stockStats.Rows.Count > 0 Then
                    Dim stockRow As DataRow = stockStats.Rows(0)
                    dashboardDt.Rows.Add("TOTAL_PRODUCTS", stockRow("TOTAL_PRODUCTS"), "STOCK")
                    dashboardDt.Rows.Add("LOW_STOCK_PRODUCTS", stockRow("LOW_STOCK_PRODUCTS"), "STOCK")
                    dashboardDt.Rows.Add("OUT_OF_STOCK_PRODUCTS", stockRow("OUT_OF_STOCK_PRODUCTS"), "STOCK")
                    dashboardDt.Rows.Add("TOTAL_STOCK_QTY", stockRow("TOTAL_STOCK_QTY"), "STOCK")
                    dashboardDt.Rows.Add("TOTAL_AVAILABLE_QTY", stockRow("TOTAL_AVAILABLE_QTY"), "STOCK")
                End If

                ' 本日統計データ追加
                If todayStats IsNot Nothing AndAlso todayStats.Rows.Count > 0 Then
                    Dim todayRow As DataRow = todayStats.Rows(0)
                    dashboardDt.Rows.Add("TODAY_RECEIPTS", todayRow("RECEIPT_COUNT"), "TODAY")
                    dashboardDt.Rows.Add("TODAY_ISSUES", todayRow("ISSUE_COUNT"), "TODAY")
                    dashboardDt.Rows.Add("TODAY_RECEIPT_QTY", todayRow("RECEIPT_QTY"), "TODAY")
                    dashboardDt.Rows.Add("TODAY_ISSUE_QTY", todayRow("ISSUE_QTY"), "TODAY")
                End If

                ' 今月統計データ追加
                If monthStats IsNot Nothing AndAlso monthStats.Rows.Count > 0 Then
                    Dim monthRow As DataRow = monthStats.Rows(0)
                    dashboardDt.Rows.Add("MONTH_RECEIPTS", monthRow("RECEIPT_COUNT"), "MONTH")
                    dashboardDt.Rows.Add("MONTH_ISSUES", monthRow("ISSUE_COUNT"), "MONTH")
                    dashboardDt.Rows.Add("MONTH_RECEIPT_QTY", monthRow("RECEIPT_QTY"), "MONTH")
                    dashboardDt.Rows.Add("MONTH_ISSUE_QTY", monthRow("ISSUE_QTY"), "MONTH")
                End If

                Return dashboardDt

            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>最近の入出庫履歴を取得</summary>
        ''' <param name="recordCount">取得件数</param>
        ''' <returns>最近の入出庫履歴データテーブル</returns>
        '''====================================================================================
        Public Function GetRecentTransactions(Optional recordCount As Integer = 10) As DataTable
            Try
                Dim sql As String = "SELECT TOP " & recordCount & " " &
                                   "TXN_DATE, TXN_TYPE, TXN_NO, PRODUCT_CD, PRODUCT_NAME, QUANTITY, UNIT, CREATE_USER " &
                                   "FROM ( " &
                                   "  SELECT RECEIPT_DATE AS TXN_DATE, '入庫' AS TXN_TYPE, RECEIPT_NO AS TXN_NO, " &
                                   "         r.PRODUCT_CD, p.PRODUCT_NAME, r.QUANTITY, p.UNIT, r.CREATE_USER, r.CREATE_DATE " &
                                   "  FROM T_RECEIPT r INNER JOIN M_PRODUCT p ON r.PRODUCT_CD = p.PRODUCT_CD " &
                                   "  UNION ALL " &
                                   "  SELECT ISSUE_DATE AS TXN_DATE, '出庫' AS TXN_TYPE, ISSUE_NO AS TXN_NO, " &
                                   "         i.PRODUCT_CD, p.PRODUCT_NAME, i.QUANTITY, p.UNIT, i.CREATE_USER, i.CREATE_DATE " &
                                   "  FROM T_ISSUE i INNER JOIN M_PRODUCT p ON i.PRODUCT_CD = p.PRODUCT_CD " &
                                   ") t " &
                                   "ORDER BY t.CREATE_DATE DESC"

                Return stockDb.GetDataTable(sql)

            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>在庫アラート情報を取得</summary>
        ''' <returns>在庫アラート情報データテーブル</returns>
        '''====================================================================================
        Public Function GetStockAlerts() As DataTable
            Try
                Return stockDb.SelectLowStockProducts()
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>カテゴリ別在庫統計を取得</summary>
        ''' <returns>カテゴリ別在庫統計データテーブル</returns>
        '''====================================================================================
        Public Function GetCategoryStockStatistics() As DataTable
            Try
                Return stockDb.GetStockStatisticsByCategory()
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>週次入出庫トレンドデータを取得</summary>
        ''' <returns>週次トレンドデータテーブル</returns>
        '''====================================================================================
        Public Function GetWeeklyTrend() As DataTable
            Try
                Dim weekStart As Date = Date.Today.AddDays(-(CInt(Date.Today.DayOfWeek) + 6) Mod 7)
                
                Dim sql As String = "SELECT " &
                                   "DATEPART(WEEKDAY, TXN_DATE) AS DAY_OF_WEEK, " &
                                   "DATENAME(WEEKDAY, TXN_DATE) AS DAY_NAME, " &
                                   "SUM(CASE WHEN TXN_TYPE = '入庫' THEN QUANTITY ELSE 0 END) AS RECEIPT_QTY, " &
                                   "SUM(CASE WHEN TXN_TYPE = '出庫' THEN QUANTITY ELSE 0 END) AS ISSUE_QTY " &
                                   "FROM ( " &
                                   "  SELECT RECEIPT_DATE AS TXN_DATE, '入庫' AS TXN_TYPE, QUANTITY " &
                                   "  FROM T_RECEIPT WHERE RECEIPT_DATE >= @WEEK_START " &
                                   "  UNION ALL " &
                                   "  SELECT ISSUE_DATE AS TXN_DATE, '出庫' AS TXN_TYPE, QUANTITY " &
                                   "  FROM T_ISSUE WHERE ISSUE_DATE >= @WEEK_START " &
                                   ") t " &
                                   "GROUP BY DATEPART(WEEKDAY, TXN_DATE), DATENAME(WEEKDAY, TXN_DATE) " &
                                   "ORDER BY DATEPART(WEEKDAY, TXN_DATE)"

                Dim parameters() As SqlParameter = {
                    New SqlParameter("@WEEK_START", weekStart)
                }

                Return stockDb.GetDataTable(sql, parameters)

            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>月次入出庫トレンドデータを取得</summary>
        ''' <param name="monthCount">取得月数</param>
        ''' <returns>月次トレンドデータテーブル</returns>
        '''====================================================================================
        Public Function GetMonthlyTrend(Optional monthCount As Integer = 6) As DataTable
            Try
                Dim sql As String = "SELECT " &
                                   "YEAR(TXN_DATE) AS TXN_YEAR, " &
                                   "MONTH(TXN_DATE) AS TXN_MONTH, " &
                                   "FORMAT(TXN_DATE, 'yyyy/MM') AS MONTH_NAME, " &
                                   "SUM(CASE WHEN TXN_TYPE = '入庫' THEN QUANTITY ELSE 0 END) AS RECEIPT_QTY, " &
                                   "SUM(CASE WHEN TXN_TYPE = '出庫' THEN QUANTITY ELSE 0 END) AS ISSUE_QTY " &
                                   "FROM ( " &
                                   "  SELECT RECEIPT_DATE AS TXN_DATE, '入庫' AS TXN_TYPE, QUANTITY " &
                                   "  FROM T_RECEIPT WHERE RECEIPT_DATE >= DATEADD(MONTH, -" & monthCount & ", GETDATE()) " &
                                   "  UNION ALL " &
                                   "  SELECT ISSUE_DATE AS TXN_DATE, '出庫' AS TXN_TYPE, QUANTITY " &
                                   "  FROM T_ISSUE WHERE ISSUE_DATE >= DATEADD(MONTH, -" & monthCount & ", GETDATE()) " &
                                   ") t " &
                                   "GROUP BY YEAR(TXN_DATE), MONTH(TXN_DATE), FORMAT(TXN_DATE, 'yyyy/MM') " &
                                   "ORDER BY YEAR(TXN_DATE), MONTH(TXN_DATE)"

                Return stockDb.GetDataTable(sql)

            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>トップ商品（出庫数上位）を取得</summary>
        ''' <param name="recordCount">取得件数</param>
        ''' <param name="days">集計日数</param>
        ''' <returns>トップ商品データテーブル</returns>
        '''====================================================================================
        Public Function GetTopProducts(Optional recordCount As Integer = 5, Optional days As Integer = 30) As DataTable
            Try
                Dim sql As String = "SELECT TOP " & recordCount & " " &
                                   "p.PRODUCT_CD, p.PRODUCT_NAME, c.CATEGORY_NAME, " &
                                   "SUM(i.QUANTITY) AS TOTAL_ISSUE_QTY, " &
                                   "COUNT(i.ISSUE_NO) AS ISSUE_COUNT, " &
                                   "s.CURRENT_STOCK, s.AVAILABLE_STOCK " &
                                   "FROM M_PRODUCT p " &
                                   "INNER JOIN M_CATEGORY c ON p.CATEGORY_CD = c.CATEGORY_CD " &
                                   "INNER JOIN T_ISSUE i ON p.PRODUCT_CD = i.PRODUCT_CD " &
                                   "LEFT JOIN T_STOCK s ON p.PRODUCT_CD = s.PRODUCT_CD " &
                                   "WHERE i.ISSUE_DATE >= DATEADD(DAY, -" & days & ", GETDATE()) " &
                                   "  AND p.ACTIVE_FLG = 1 " &
                                   "GROUP BY p.PRODUCT_CD, p.PRODUCT_NAME, c.CATEGORY_NAME, s.CURRENT_STOCK, s.AVAILABLE_STOCK " &
                                   "ORDER BY SUM(i.QUANTITY) DESC"

                Return stockDb.GetDataTable(sql)

            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

    End Class

End Namespace
