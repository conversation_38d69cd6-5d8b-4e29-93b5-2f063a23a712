Imports System.Data
Imports System.Data.SqlClient
Imports Whouse.Application.Logic.Database.Transaction
Imports Whouse.Application.Logic.Database.Product
Imports Whouse.Application.Logic.Database.Stock

Namespace Transaction

    '''====================================================================================
    ''' <summary>入出庫管理ビジネスロジッククラス</summary>
    '''====================================================================================
    Public Class TransactionLogic
        Inherits Dbs.Asphalt.Database.BaseClass.LogicBase

        Private transactionDb As TransactionDatabase
        Private productDb As ProductDatabase
        Private stockDb As StockDatabase

        '''====================================================================================
        ''' <summary>コンストラクタ</summary>
        ''' <param name="security">セキュリティオブジェクト</param>
        '''====================================================================================
        Public Sub New(security As Dbs.Asphalt.Core.SystemLogic.Security)
            MyBase.New(security)
            transactionDb = New TransactionDatabase()
            productDb = New ProductDatabase()
            stockDb = New StockDatabase()
        End Sub

        '''====================================================================================
        ''' <summary>入庫処理を実行</summary>
        ''' <param name="receiptDate">入庫日</param>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="quantity">入庫数量</param>
        ''' <param name="unitCost">仕入単価</param>
        ''' <param name="supplier">仕入先</param>
        ''' <param name="remarks">備考</param>
        ''' <returns>処理結果</returns>
        '''====================================================================================
        Public Function ProcessReceipt(receiptDate As Date, productCode As String, quantity As Integer,
                                      unitCost As Decimal?, supplier As String, remarks As String) As Boolean
            Try
                ' 入力値検証
                If Not ValidateReceiptInput(receiptDate, productCode, quantity) Then
                    Return False
                End If

                ' 商品存在チェック
                If productDb.SelectByCode(productCode) Is Nothing Then
                    LastError = "指定された商品が存在しません。"
                    Return False
                End If

                ' 入庫番号生成
                Dim receiptNo As String = GenerateReceiptNumber()
                If String.IsNullOrEmpty(receiptNo) Then
                    LastError = "入庫番号の生成に失敗しました。"
                    Return False
                End If

                ' ストアドプロシージャを使用して入庫処理
                Dim sql As String = "EXEC SP_RECEIVE_STOCK @RECEIPT_NO, @RECEIPT_DATE, @PRODUCT_CD, @QUANTITY, @UNIT_COST, @SUPPLIER, @REMARKS, @USER_ID"
                Dim parameters() As SqlParameter = {
                    New SqlParameter("@RECEIPT_NO", receiptNo),
                    New SqlParameter("@RECEIPT_DATE", receiptDate),
                    New SqlParameter("@PRODUCT_CD", productCode),
                    New SqlParameter("@QUANTITY", quantity),
                    New SqlParameter("@UNIT_COST", If(unitCost.HasValue, CType(unitCost.Value, Object), DBNull.Value)),
                    New SqlParameter("@SUPPLIER", If(String.IsNullOrEmpty(supplier), DBNull.Value, supplier)),
                    New SqlParameter("@REMARKS", If(String.IsNullOrEmpty(remarks), DBNull.Value, remarks)),
                    New SqlParameter("@USER_ID", Security.UserID)
                }

                transactionDb.ExecuteNonQuery(sql, parameters)
                LastMessage = $"入庫処理が完了しました。入庫番号: {receiptNo}"
                Return True

            Catch ex As Exception
                LastError = ex.Message
                Return False
            End Try
        End Function

        '''====================================================================================
        ''' <summary>出庫処理を実行</summary>
        ''' <param name="issueDate">出庫日</param>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="quantity">出庫数量</param>
        ''' <param name="issueTo">出庫先</param>
        ''' <param name="remarks">備考</param>
        ''' <returns>処理結果</returns>
        '''====================================================================================
        Public Function ProcessIssue(issueDate As Date, productCode As String, quantity As Integer,
                                    issueTo As String, remarks As String) As Boolean
            Try
                ' 入力値検証
                If Not ValidateIssueInput(issueDate, productCode, quantity) Then
                    Return False
                End If

                ' 商品存在チェック
                If productDb.SelectByCode(productCode) Is Nothing Then
                    LastError = "指定された商品が存在しません。"
                    Return False
                End If

                ' 在庫数チェック
                Dim availableStock As Integer = GetAvailableStock(productCode)
                If availableStock < quantity Then
                    LastError = $"在庫不足です。利用可能在庫: {availableStock}, 出庫要求: {quantity}"
                    Return False
                End If

                ' 出庫番号生成
                Dim issueNo As String = GenerateIssueNumber()
                If String.IsNullOrEmpty(issueNo) Then
                    LastError = "出庫番号の生成に失敗しました。"
                    Return False
                End If

                ' ストアドプロシージャを使用して出庫処理
                Dim sql As String = "EXEC SP_ISSUE_STOCK @ISSUE_NO, @ISSUE_DATE, @PRODUCT_CD, @QUANTITY, @ISSUE_TO, @REMARKS, @USER_ID"
                Dim parameters() As SqlParameter = {
                    New SqlParameter("@ISSUE_NO", issueNo),
                    New SqlParameter("@ISSUE_DATE", issueDate),
                    New SqlParameter("@PRODUCT_CD", productCode),
                    New SqlParameter("@QUANTITY", quantity),
                    New SqlParameter("@ISSUE_TO", If(String.IsNullOrEmpty(issueTo), DBNull.Value, issueTo)),
                    New SqlParameter("@REMARKS", If(String.IsNullOrEmpty(remarks), DBNull.Value, remarks)),
                    New SqlParameter("@USER_ID", Security.UserID)
                }

                transactionDb.ExecuteNonQuery(sql, parameters)
                LastMessage = $"出庫処理が完了しました。出庫番号: {issueNo}"
                Return True

            Catch ex As Exception
                LastError = ex.Message
                Return False
            End Try
        End Function

        '''====================================================================================
        ''' <summary>入庫履歴を取得</summary>
        ''' <param name="dateFrom">開始日</param>
        ''' <param name="dateTo">終了日</param>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="supplier">仕入先</param>
        ''' <returns>入庫履歴データテーブル</returns>
        '''====================================================================================
        Public Function GetReceiptHistory(dateFrom As Date?, dateTo As Date?, Optional productCode As String = "", Optional supplier As String = "") As DataTable
            Try
                Return transactionDb.SearchReceipts(dateFrom, dateTo, productCode, supplier)
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>出庫履歴を取得</summary>
        ''' <param name="dateFrom">開始日</param>
        ''' <param name="dateTo">終了日</param>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="issueTo">出庫先</param>
        ''' <returns>出庫履歴データテーブル</returns>
        '''====================================================================================
        Public Function GetIssueHistory(dateFrom As Date?, dateTo As Date?, Optional productCode As String = "", Optional issueTo As String = "") As DataTable
            Try
                Return transactionDb.SearchIssues(dateFrom, dateTo, productCode, issueTo)
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>入出庫統計を取得</summary>
        ''' <param name="dateFrom">開始日</param>
        ''' <param name="dateTo">終了日</param>
        ''' <returns>入出庫統計データテーブル</returns>
        '''====================================================================================
        Public Function GetTransactionStatistics(dateFrom As Date, dateTo As Date) As DataTable
            Try
                Return transactionDb.GetTransactionStatistics(dateFrom, dateTo)
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品別入出庫統計を取得</summary>
        ''' <param name="dateFrom">開始日</param>
        ''' <param name="dateTo">終了日</param>
        ''' <returns>商品別入出庫統計データテーブル</returns>
        '''====================================================================================
        Public Function GetTransactionStatisticsByProduct(dateFrom As Date, dateTo As Date) As DataTable
            Try
                Return transactionDb.GetTransactionStatisticsByProduct(dateFrom, dateTo)
            Catch ex As Exception
                LastError = ex.Message
                Return Nothing
            End Try
        End Function

        '''====================================================================================
        ''' <summary>利用可能在庫数を取得</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <returns>利用可能在庫数</returns>
        '''====================================================================================
        Private Function GetAvailableStock(productCode As String) As Integer
            Try
                Dim stockRow As DataRow = stockDb.SelectByProductCode(productCode)
                If stockRow IsNot Nothing Then
                    Return CInt(stockRow("AVAILABLE_STOCK"))
                Else
                    Return 0
                End If
            Catch ex As Exception
                Return 0
            End Try
        End Function

        '''====================================================================================
        ''' <summary>入庫番号を生成</summary>
        ''' <returns>入庫番号</returns>
        '''====================================================================================
        Private Function GenerateReceiptNumber() As String
            Try
                Dim sql As String = "EXEC SP_GET_NEXT_NUMBER @NUMBER_TYPE, @USER_ID, @NEXT_NUMBER OUTPUT"
                Dim parameters() As SqlParameter = {
                    New SqlParameter("@NUMBER_TYPE", "RECEIPT"),
                    New SqlParameter("@USER_ID", Security.UserID),
                    New SqlParameter("@NEXT_NUMBER", SqlDbType.NVarChar, 50) With {.Direction = ParameterDirection.Output}
                }

                transactionDb.ExecuteNonQuery(sql, parameters)
                Return parameters(2).Value.ToString()

            Catch ex As Exception
                LastError = "入庫番号生成でエラーが発生しました: " & ex.Message
                Return ""
            End Try
        End Function

        '''====================================================================================
        ''' <summary>出庫番号を生成</summary>
        ''' <returns>出庫番号</returns>
        '''====================================================================================
        Private Function GenerateIssueNumber() As String
            Try
                Dim sql As String = "EXEC SP_GET_NEXT_NUMBER @NUMBER_TYPE, @USER_ID, @NEXT_NUMBER OUTPUT"
                Dim parameters() As SqlParameter = {
                    New SqlParameter("@NUMBER_TYPE", "ISSUE"),
                    New SqlParameter("@USER_ID", Security.UserID),
                    New SqlParameter("@NEXT_NUMBER", SqlDbType.NVarChar, 50) With {.Direction = ParameterDirection.Output}
                }

                transactionDb.ExecuteNonQuery(sql, parameters)
                Return parameters(2).Value.ToString()

            Catch ex As Exception
                LastError = "出庫番号生成でエラーが発生しました: " & ex.Message
                Return ""
            End Try
        End Function

        '''====================================================================================
        ''' <summary>入庫入力値検証</summary>
        ''' <param name="receiptDate">入庫日</param>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="quantity">入庫数量</param>
        ''' <returns>検証結果</returns>
        '''====================================================================================
        Private Function ValidateReceiptInput(receiptDate As Date, productCode As String, quantity As Integer) As Boolean
            If String.IsNullOrEmpty(productCode) Then
                LastError = "商品コードを入力してください。"
                Return False
            End If

            If quantity <= 0 Then
                LastError = "入庫数量は1以上で入力してください。"
                Return False
            End If

            If receiptDate > Date.Today Then
                LastError = "入庫日は今日以前の日付で入力してください。"
                Return False
            End If

            Return True
        End Function

        '''====================================================================================
        ''' <summary>出庫入力値検証</summary>
        ''' <param name="issueDate">出庫日</param>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="quantity">出庫数量</param>
        ''' <returns>検証結果</returns>
        '''====================================================================================
        Private Function ValidateIssueInput(issueDate As Date, productCode As String, quantity As Integer) As Boolean
            If String.IsNullOrEmpty(productCode) Then
                LastError = "商品コードを入力してください。"
                Return False
            End If

            If quantity <= 0 Then
                LastError = "出庫数量は1以上で入力してください。"
                Return False
            End If

            If issueDate > Date.Today Then
                LastError = "出庫日は今日以前の日付で入力してください。"
                Return False
            End If

            Return True
        End Function

    End Class

End Namespace
