Imports System.Data
Imports System.Data.SqlClient

Namespace Product

    '''====================================================================================
    ''' <summary>商品マスタデータアクセスクラス</summary>
    '''====================================================================================
    Public Class ProductDatabase
        Inherits Dbs.Asphalt.Database.BaseClass.DatabaseBase

        '''====================================================================================
        ''' <summary>全商品を取得</summary>
        ''' <returns>商品データテーブル</returns>
        '''====================================================================================
        Public Function SelectAll() As DataTable
            Try
                Dim sql As String = "SELECT p.PRODUCT_CD, p.PRODUCT_NAME, p.CATEGORY_CD, c.CATEGORY_NAME, " &
                                   "p.UNIT_PRICE, p.MIN_STOCK, p.UNIT, p.DESCRIPTION, p.ACTIVE_FLG, " &
                                   "p.CREATE_DATE, p.CREATE_USER, p.UPDATE_DATE, p.UPDATE_USER " &
                                   "FROM M_PRODUCT p " &
                                   "INNER JOIN M_CATEGORY c ON p.CATEGORY_CD = c.CATEGORY_CD " &
                                   "WHERE p.ACTIVE_FLG = 1 " &
                                   "ORDER BY p.PRODUCT_CD"

                Return MyBase.GetDataTable(sql)

            Catch ex As Exception
                Throw New Exception("商品一覧取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品コードで商品を取得</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <returns>商品データ行</returns>
        '''====================================================================================
        Public Function SelectByCode(productCode As String) As DataRow
            Try
                Dim sql As String = "SELECT p.PRODUCT_CD, p.PRODUCT_NAME, p.CATEGORY_CD, c.CATEGORY_NAME, " &
                                   "p.UNIT_PRICE, p.MIN_STOCK, p.UNIT, p.DESCRIPTION, p.ACTIVE_FLG, " &
                                   "p.CREATE_DATE, p.CREATE_USER, p.UPDATE_DATE, p.UPDATE_USER " &
                                   "FROM M_PRODUCT p " &
                                   "INNER JOIN M_CATEGORY c ON p.CATEGORY_CD = c.CATEGORY_CD " &
                                   "WHERE p.PRODUCT_CD = @PRODUCT_CD"

                Dim parameters As New List(Of SqlParameter)
                parameters.Add(New SqlParameter("@PRODUCT_CD", productCode))

                Dim dt As DataTable = MyBase.GetDataTable(sql, parameters.ToArray())
                If dt.Rows.Count > 0 Then
                    Return dt.Rows(0)
                Else
                    Return Nothing
                End If

            Catch ex As Exception
                Throw New Exception("商品取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品を検索</summary>
        ''' <param name="keyword">検索キーワード</param>
        ''' <param name="categoryCode">カテゴリコード（省略可）</param>
        ''' <returns>商品データテーブル</returns>
        '''====================================================================================
        Public Function Search(keyword As String, Optional categoryCode As String = "") As DataTable
            Try
                Dim sql As String = "SELECT p.PRODUCT_CD, p.PRODUCT_NAME, p.CATEGORY_CD, c.CATEGORY_NAME, " &
                                   "p.UNIT_PRICE, p.MIN_STOCK, p.UNIT, p.DESCRIPTION, p.ACTIVE_FLG, " &
                                   "p.CREATE_DATE, p.CREATE_USER, p.UPDATE_DATE, p.UPDATE_USER " &
                                   "FROM M_PRODUCT p " &
                                   "INNER JOIN M_CATEGORY c ON p.CATEGORY_CD = c.CATEGORY_CD " &
                                   "WHERE p.ACTIVE_FLG = 1"

                Dim parameters As New List(Of SqlParameter)

                If Not String.IsNullOrEmpty(keyword) Then
                    sql &= " AND (p.PRODUCT_CD LIKE @KEYWORD OR p.PRODUCT_NAME LIKE @KEYWORD)"
                    parameters.Add(New SqlParameter("@KEYWORD", "%" & keyword & "%"))
                End If

                If Not String.IsNullOrEmpty(categoryCode) Then
                    sql &= " AND p.CATEGORY_CD = @CATEGORY_CD"
                    parameters.Add(New SqlParameter("@CATEGORY_CD", categoryCode))
                End If

                sql &= " ORDER BY p.PRODUCT_CD"

                Return MyBase.GetDataTable(sql, parameters.ToArray())

            Catch ex As Exception
                Throw New Exception("商品検索でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品を登録</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="productName">商品名</param>
        ''' <param name="categoryCode">カテゴリコード</param>
        ''' <param name="unitPrice">単価</param>
        ''' <param name="minStock">最小在庫数</param>
        ''' <param name="unit">単位</param>
        ''' <param name="description">説明</param>
        ''' <param name="userId">ユーザーID</param>
        ''' <returns>処理結果</returns>
        '''====================================================================================
        Public Function Insert(productCode As String, productName As String, categoryCode As String,
                              unitPrice As Decimal, minStock As Integer, unit As String,
                              description As String, userId As String) As Boolean
            Try
                Dim sql As String = "INSERT INTO M_PRODUCT (PRODUCT_CD, PRODUCT_NAME, CATEGORY_CD, UNIT_PRICE, " &
                                   "MIN_STOCK, UNIT, DESCRIPTION, ACTIVE_FLG, CREATE_USER, UPDATE_USER) " &
                                   "VALUES (@PRODUCT_CD, @PRODUCT_NAME, @CATEGORY_CD, @UNIT_PRICE, " &
                                   "@MIN_STOCK, @UNIT, @DESCRIPTION, 1, @USER_ID, @USER_ID)"

                Dim parameters() As SqlParameter = {
                    New SqlParameter("@PRODUCT_CD", productCode),
                    New SqlParameter("@PRODUCT_NAME", productName),
                    New SqlParameter("@CATEGORY_CD", categoryCode),
                    New SqlParameter("@UNIT_PRICE", unitPrice),
                    New SqlParameter("@MIN_STOCK", minStock),
                    New SqlParameter("@UNIT", unit),
                    New SqlParameter("@DESCRIPTION", If(String.IsNullOrEmpty(description), DBNull.Value, description)),
                    New SqlParameter("@USER_ID", userId)
                }

                Return MyBase.ExecuteNonQuery(sql, parameters) > 0

            Catch ex As Exception
                Throw New Exception("商品登録でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品を更新</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="productName">商品名</param>
        ''' <param name="categoryCode">カテゴリコード</param>
        ''' <param name="unitPrice">単価</param>
        ''' <param name="minStock">最小在庫数</param>
        ''' <param name="unit">単位</param>
        ''' <param name="description">説明</param>
        ''' <param name="userId">ユーザーID</param>
        ''' <returns>処理結果</returns>
        '''====================================================================================
        Public Function Update(productCode As String, productName As String, categoryCode As String,
                              unitPrice As Decimal, minStock As Integer, unit As String,
                              description As String, userId As String) As Boolean
            Try
                Dim sql As String = "UPDATE M_PRODUCT SET PRODUCT_NAME = @PRODUCT_NAME, CATEGORY_CD = @CATEGORY_CD, " &
                                   "UNIT_PRICE = @UNIT_PRICE, MIN_STOCK = @MIN_STOCK, UNIT = @UNIT, " &
                                   "DESCRIPTION = @DESCRIPTION, UPDATE_DATE = GETDATE(), UPDATE_USER = @USER_ID " &
                                   "WHERE PRODUCT_CD = @PRODUCT_CD"

                Dim parameters() As SqlParameter = {
                    New SqlParameter("@PRODUCT_CD", productCode),
                    New SqlParameter("@PRODUCT_NAME", productName),
                    New SqlParameter("@CATEGORY_CD", categoryCode),
                    New SqlParameter("@UNIT_PRICE", unitPrice),
                    New SqlParameter("@MIN_STOCK", minStock),
                    New SqlParameter("@UNIT", unit),
                    New SqlParameter("@DESCRIPTION", If(String.IsNullOrEmpty(description), DBNull.Value, description)),
                    New SqlParameter("@USER_ID", userId)
                }

                Return MyBase.ExecuteNonQuery(sql, parameters) > 0

            Catch ex As Exception
                Throw New Exception("商品更新でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品を論理削除</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="userId">ユーザーID</param>
        ''' <returns>処理結果</returns>
        '''====================================================================================
        Public Function Delete(productCode As String, userId As String) As Boolean
            Try
                ' 在庫がある場合は削除不可
                Dim stockCheckSql As String = "SELECT CURRENT_STOCK FROM T_STOCK WHERE PRODUCT_CD = @PRODUCT_CD"
                Dim stockParams() As SqlParameter = {New SqlParameter("@PRODUCT_CD", productCode)}
                Dim stockDt As DataTable = MyBase.GetDataTable(stockCheckSql, stockParams)

                If stockDt.Rows.Count > 0 AndAlso CInt(stockDt.Rows(0)("CURRENT_STOCK")) > 0 Then
                    Throw New Exception("在庫がある商品は削除できません。")
                End If

                ' 論理削除実行
                Dim sql As String = "UPDATE M_PRODUCT SET ACTIVE_FLG = 0, UPDATE_DATE = GETDATE(), UPDATE_USER = @USER_ID " &
                                   "WHERE PRODUCT_CD = @PRODUCT_CD"

                Dim parameters() As SqlParameter = {
                    New SqlParameter("@PRODUCT_CD", productCode),
                    New SqlParameter("@USER_ID", userId)
                }

                Return MyBase.ExecuteNonQuery(sql, parameters) > 0

            Catch ex As Exception
                Throw New Exception("商品削除でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品コードの重複チェック</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <returns>重複している場合True</returns>
        '''====================================================================================
        Public Function ExistsProductCode(productCode As String) As Boolean
            Try
                Dim sql As String = "SELECT COUNT(*) FROM M_PRODUCT WHERE PRODUCT_CD = @PRODUCT_CD"
                Dim parameters() As SqlParameter = {New SqlParameter("@PRODUCT_CD", productCode)}

                Dim count As Integer = CInt(MyBase.ExecuteScalar(sql, parameters))
                Return count > 0

            Catch ex As Exception
                Throw New Exception("商品コード重複チェックでエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

    End Class

End Namespace
