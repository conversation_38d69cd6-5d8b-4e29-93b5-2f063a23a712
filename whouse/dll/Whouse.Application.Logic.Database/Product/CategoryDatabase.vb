Imports System.Data
Imports System.Data.SqlClient

Namespace Product

    '''====================================================================================
    ''' <summary>カテゴリマスタデータアクセスクラス</summary>
    '''====================================================================================
    Public Class CategoryDatabase
        Inherits Dbs.Asphalt.Database.BaseClass.DatabaseBase

        '''====================================================================================
        ''' <summary>全カテゴリを取得</summary>
        ''' <returns>カテゴリデータテーブル</returns>
        '''====================================================================================
        Public Function SelectAll() As DataTable
            Try
                Dim sql As String = "SELECT CATEGORY_CD, CATEGORY_NAME, SORT_ORDER, ACTIVE_FLG, " &
                                   "CREATE_DATE, CREATE_USER, UPDATE_DATE, UPDATE_USER " &
                                   "FROM M_CATEGORY " &
                                   "WHERE ACTIVE_FLG = 1 " &
                                   "ORDER BY SORT_ORDER, CATEGORY_CD"

                Return MyBase.GetDataTable(sql)

            Catch ex As Exception
                Throw New Exception("カテゴリ一覧取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>カテゴリコードでカテゴリを取得</summary>
        ''' <param name="categoryCode">カテゴリコード</param>
        ''' <returns>カテゴリデータ行</returns>
        '''====================================================================================
        Public Function SelectByCode(categoryCode As String) As DataRow
            Try
                Dim sql As String = "SELECT CATEGORY_CD, CATEGORY_NAME, SORT_ORDER, ACTIVE_FLG, " &
                                   "CREATE_DATE, CREATE_USER, UPDATE_DATE, UPDATE_USER " &
                                   "FROM M_CATEGORY " &
                                   "WHERE CATEGORY_CD = @CATEGORY_CD"

                Dim parameters As New List(Of SqlParameter)
                parameters.Add(New SqlParameter("@CATEGORY_CD", categoryCode))

                Dim dt As DataTable = MyBase.GetDataTable(sql, parameters.ToArray())
                If dt.Rows.Count > 0 Then
                    Return dt.Rows(0)
                Else
                    Return Nothing
                End If

            Catch ex As Exception
                Throw New Exception("カテゴリ取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>ドロップダウン用カテゴリリストを取得</summary>
        ''' <returns>カテゴリデータテーブル</returns>
        '''====================================================================================
        Public Function SelectForDropDown() As DataTable
            Try
                Dim sql As String = "SELECT CATEGORY_CD, CATEGORY_NAME " &
                                   "FROM M_CATEGORY " &
                                   "WHERE ACTIVE_FLG = 1 " &
                                   "ORDER BY SORT_ORDER, CATEGORY_CD"

                Return MyBase.GetDataTable(sql)

            Catch ex As Exception
                Throw New Exception("カテゴリドロップダウンリスト取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>カテゴリを登録</summary>
        ''' <param name="categoryCode">カテゴリコード</param>
        ''' <param name="categoryName">カテゴリ名</param>
        ''' <param name="sortOrder">表示順</param>
        ''' <param name="userId">ユーザーID</param>
        ''' <returns>処理結果</returns>
        '''====================================================================================
        Public Function Insert(categoryCode As String, categoryName As String, sortOrder As Integer, userId As String) As Boolean
            Try
                Dim sql As String = "INSERT INTO M_CATEGORY (CATEGORY_CD, CATEGORY_NAME, SORT_ORDER, " &
                                   "ACTIVE_FLG, CREATE_USER, UPDATE_USER) " &
                                   "VALUES (@CATEGORY_CD, @CATEGORY_NAME, @SORT_ORDER, 1, @USER_ID, @USER_ID)"

                Dim parameters() As SqlParameter = {
                    New SqlParameter("@CATEGORY_CD", categoryCode),
                    New SqlParameter("@CATEGORY_NAME", categoryName),
                    New SqlParameter("@SORT_ORDER", sortOrder),
                    New SqlParameter("@USER_ID", userId)
                }

                Return MyBase.ExecuteNonQuery(sql, parameters) > 0

            Catch ex As Exception
                Throw New Exception("カテゴリ登録でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>カテゴリを更新</summary>
        ''' <param name="categoryCode">カテゴリコード</param>
        ''' <param name="categoryName">カテゴリ名</param>
        ''' <param name="sortOrder">表示順</param>
        ''' <param name="userId">ユーザーID</param>
        ''' <returns>処理結果</returns>
        '''====================================================================================
        Public Function Update(categoryCode As String, categoryName As String, sortOrder As Integer, userId As String) As Boolean
            Try
                Dim sql As String = "UPDATE M_CATEGORY SET CATEGORY_NAME = @CATEGORY_NAME, SORT_ORDER = @SORT_ORDER, " &
                                   "UPDATE_DATE = GETDATE(), UPDATE_USER = @USER_ID " &
                                   "WHERE CATEGORY_CD = @CATEGORY_CD"

                Dim parameters() As SqlParameter = {
                    New SqlParameter("@CATEGORY_CD", categoryCode),
                    New SqlParameter("@CATEGORY_NAME", categoryName),
                    New SqlParameter("@SORT_ORDER", sortOrder),
                    New SqlParameter("@USER_ID", userId)
                }

                Return MyBase.ExecuteNonQuery(sql, parameters) > 0

            Catch ex As Exception
                Throw New Exception("カテゴリ更新でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>カテゴリを論理削除</summary>
        ''' <param name="categoryCode">カテゴリコード</param>
        ''' <param name="userId">ユーザーID</param>
        ''' <returns>処理結果</returns>
        '''====================================================================================
        Public Function Delete(categoryCode As String, userId As String) As Boolean
            Try
                ' 使用中の商品がある場合は削除不可
                Dim productCheckSql As String = "SELECT COUNT(*) FROM M_PRODUCT WHERE CATEGORY_CD = @CATEGORY_CD AND ACTIVE_FLG = 1"
                Dim productParams() As SqlParameter = {New SqlParameter("@CATEGORY_CD", categoryCode)}
                Dim productCount As Integer = CInt(MyBase.ExecuteScalar(productCheckSql, productParams))

                If productCount > 0 Then
                    Throw New Exception("このカテゴリを使用している商品があるため削除できません。")
                End If

                ' 論理削除実行
                Dim sql As String = "UPDATE M_CATEGORY SET ACTIVE_FLG = 0, UPDATE_DATE = GETDATE(), UPDATE_USER = @USER_ID " &
                                   "WHERE CATEGORY_CD = @CATEGORY_CD"

                Dim parameters() As SqlParameter = {
                    New SqlParameter("@CATEGORY_CD", categoryCode),
                    New SqlParameter("@USER_ID", userId)
                }

                Return MyBase.ExecuteNonQuery(sql, parameters) > 0

            Catch ex As Exception
                Throw New Exception("カテゴリ削除でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>カテゴリコードの重複チェック</summary>
        ''' <param name="categoryCode">カテゴリコード</param>
        ''' <returns>重複している場合True</returns>
        '''====================================================================================
        Public Function ExistsCategoryCode(categoryCode As String) As Boolean
            Try
                Dim sql As String = "SELECT COUNT(*) FROM M_CATEGORY WHERE CATEGORY_CD = @CATEGORY_CD"
                Dim parameters() As SqlParameter = {New SqlParameter("@CATEGORY_CD", categoryCode)}

                Dim count As Integer = CInt(MyBase.ExecuteScalar(sql, parameters))
                Return count > 0

            Catch ex As Exception
                Throw New Exception("カテゴリコード重複チェックでエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>次の表示順を取得</summary>
        ''' <returns>次の表示順</returns>
        '''====================================================================================
        Public Function GetNextSortOrder() As Integer
            Try
                Dim sql As String = "SELECT ISNULL(MAX(SORT_ORDER), 0) + 1 FROM M_CATEGORY"
                Return CInt(MyBase.ExecuteScalar(sql))

            Catch ex As Exception
                Throw New Exception("次の表示順取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

    End Class

End Namespace
