Imports System.Data
Imports System.Data.SqlClient

Namespace Transaction

    '''====================================================================================
    ''' <summary>入出庫トランザクションデータアクセスクラス</summary>
    '''====================================================================================
    Public Class TransactionDatabase
        Inherits Dbs.Asphalt.Database.BaseClass.DatabaseBase

        '''====================================================================================
        ''' <summary>入庫履歴を全件取得</summary>
        ''' <returns>入庫履歴データテーブル</returns>
        '''====================================================================================
        Public Function SelectAllReceipts() As DataTable
            Try
                Dim sql As String = "SELECT r.RECEIPT_NO, r.RECEIPT_DATE, r.PRODUCT_CD, p.PRODUCT_NAME, " &
                                   "r.QUANTITY, r.UNIT_COST, r.SUPPLIER, r.REMARKS, " &
                                   "r.CREATE_DATE, r.CREATE_USER, p.UNIT " &
                                   "FROM T_RECEIPT r " &
                                   "INNER JOIN M_PRODUCT p ON r.PRODUCT_CD = p.PRODUCT_CD " &
                                   "ORDER BY r.RECEIPT_DATE DESC, r.RECEIPT_NO DESC"

                Return MyBase.GetDataTable(sql)

            Catch ex As Exception
                Throw New Exception("入庫履歴取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>出庫履歴を全件取得</summary>
        ''' <returns>出庫履歴データテーブル</returns>
        '''====================================================================================
        Public Function SelectAllIssues() As DataTable
            Try
                Dim sql As String = "SELECT i.ISSUE_NO, i.ISSUE_DATE, i.PRODUCT_CD, p.PRODUCT_NAME, " &
                                   "i.QUANTITY, i.ISSUE_TO, i.REMARKS, " &
                                   "i.CREATE_DATE, i.CREATE_USER, p.UNIT " &
                                   "FROM T_ISSUE i " &
                                   "INNER JOIN M_PRODUCT p ON i.PRODUCT_CD = p.PRODUCT_CD " &
                                   "ORDER BY i.ISSUE_DATE DESC, i.ISSUE_NO DESC"

                Return MyBase.GetDataTable(sql)

            Catch ex As Exception
                Throw New Exception("出庫履歴取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>入庫履歴を検索</summary>
        ''' <param name="dateFrom">開始日</param>
        ''' <param name="dateTo">終了日</param>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="supplier">仕入先</param>
        ''' <returns>入庫履歴データテーブル</returns>
        '''====================================================================================
        Public Function SearchReceipts(dateFrom As Date?, dateTo As Date?, Optional productCode As String = "", Optional supplier As String = "") As DataTable
            Try
                Dim sql As String = "SELECT r.RECEIPT_NO, r.RECEIPT_DATE, r.PRODUCT_CD, p.PRODUCT_NAME, " &
                                   "r.QUANTITY, r.UNIT_COST, r.SUPPLIER, r.REMARKS, " &
                                   "r.CREATE_DATE, r.CREATE_USER, p.UNIT " &
                                   "FROM T_RECEIPT r " &
                                   "INNER JOIN M_PRODUCT p ON r.PRODUCT_CD = p.PRODUCT_CD " &
                                   "WHERE 1=1"

                Dim parameters As New List(Of SqlParameter)

                If dateFrom.HasValue Then
                    sql &= " AND r.RECEIPT_DATE >= @DATE_FROM"
                    parameters.Add(New SqlParameter("@DATE_FROM", dateFrom.Value))
                End If

                If dateTo.HasValue Then
                    sql &= " AND r.RECEIPT_DATE <= @DATE_TO"
                    parameters.Add(New SqlParameter("@DATE_TO", dateTo.Value))
                End If

                If Not String.IsNullOrEmpty(productCode) Then
                    sql &= " AND (r.PRODUCT_CD LIKE @PRODUCT_CODE OR p.PRODUCT_NAME LIKE @PRODUCT_CODE)"
                    parameters.Add(New SqlParameter("@PRODUCT_CODE", "%" & productCode & "%"))
                End If

                If Not String.IsNullOrEmpty(supplier) Then
                    sql &= " AND r.SUPPLIER LIKE @SUPPLIER"
                    parameters.Add(New SqlParameter("@SUPPLIER", "%" & supplier & "%"))
                End If

                sql &= " ORDER BY r.RECEIPT_DATE DESC, r.RECEIPT_NO DESC"

                Return MyBase.GetDataTable(sql, parameters.ToArray())

            Catch ex As Exception
                Throw New Exception("入庫履歴検索でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>出庫履歴を検索</summary>
        ''' <param name="dateFrom">開始日</param>
        ''' <param name="dateTo">終了日</param>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="issueTo">出庫先</param>
        ''' <returns>出庫履歴データテーブル</returns>
        '''====================================================================================
        Public Function SearchIssues(dateFrom As Date?, dateTo As Date?, Optional productCode As String = "", Optional issueTo As String = "") As DataTable
            Try
                Dim sql As String = "SELECT i.ISSUE_NO, i.ISSUE_DATE, i.PRODUCT_CD, p.PRODUCT_NAME, " &
                                   "i.QUANTITY, i.ISSUE_TO, i.REMARKS, " &
                                   "i.CREATE_DATE, i.CREATE_USER, p.UNIT " &
                                   "FROM T_ISSUE i " &
                                   "INNER JOIN M_PRODUCT p ON i.PRODUCT_CD = p.PRODUCT_CD " &
                                   "WHERE 1=1"

                Dim parameters As New List(Of SqlParameter)

                If dateFrom.HasValue Then
                    sql &= " AND i.ISSUE_DATE >= @DATE_FROM"
                    parameters.Add(New SqlParameter("@DATE_FROM", dateFrom.Value))
                End If

                If dateTo.HasValue Then
                    sql &= " AND i.ISSUE_DATE <= @DATE_TO"
                    parameters.Add(New SqlParameter("@DATE_TO", dateTo.Value))
                End If

                If Not String.IsNullOrEmpty(productCode) Then
                    sql &= " AND (i.PRODUCT_CD LIKE @PRODUCT_CODE OR p.PRODUCT_NAME LIKE @PRODUCT_CODE)"
                    parameters.Add(New SqlParameter("@PRODUCT_CODE", "%" & productCode & "%"))
                End If

                If Not String.IsNullOrEmpty(issueTo) Then
                    sql &= " AND i.ISSUE_TO LIKE @ISSUE_TO"
                    parameters.Add(New SqlParameter("@ISSUE_TO", "%" & issueTo & "%"))
                End If

                sql &= " ORDER BY i.ISSUE_DATE DESC, i.ISSUE_NO DESC"

                Return MyBase.GetDataTable(sql, parameters.ToArray())

            Catch ex As Exception
                Throw New Exception("出庫履歴検索でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>入庫番号で入庫データを取得</summary>
        ''' <param name="receiptNo">入庫番号</param>
        ''' <returns>入庫データ行</returns>
        '''====================================================================================
        Public Function SelectReceiptByNo(receiptNo As String) As DataRow
            Try
                Dim sql As String = "SELECT r.RECEIPT_NO, r.RECEIPT_DATE, r.PRODUCT_CD, p.PRODUCT_NAME, " &
                                   "r.QUANTITY, r.UNIT_COST, r.SUPPLIER, r.REMARKS, " &
                                   "r.CREATE_DATE, r.CREATE_USER, p.UNIT " &
                                   "FROM T_RECEIPT r " &
                                   "INNER JOIN M_PRODUCT p ON r.PRODUCT_CD = p.PRODUCT_CD " &
                                   "WHERE r.RECEIPT_NO = @RECEIPT_NO"

                Dim parameters() As SqlParameter = {New SqlParameter("@RECEIPT_NO", receiptNo)}

                Dim dt As DataTable = MyBase.GetDataTable(sql, parameters)
                If dt.Rows.Count > 0 Then
                    Return dt.Rows(0)
                Else
                    Return Nothing
                End If

            Catch ex As Exception
                Throw New Exception("入庫データ取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>出庫番号で出庫データを取得</summary>
        ''' <param name="issueNo">出庫番号</param>
        ''' <returns>出庫データ行</returns>
        '''====================================================================================
        Public Function SelectIssueByNo(issueNo As String) As DataRow
            Try
                Dim sql As String = "SELECT i.ISSUE_NO, i.ISSUE_DATE, i.PRODUCT_CD, p.PRODUCT_NAME, " &
                                   "i.QUANTITY, i.ISSUE_TO, i.REMARKS, " &
                                   "i.CREATE_DATE, i.CREATE_USER, p.UNIT " &
                                   "FROM T_ISSUE i " &
                                   "INNER JOIN M_PRODUCT p ON i.PRODUCT_CD = p.PRODUCT_CD " &
                                   "WHERE i.ISSUE_NO = @ISSUE_NO"

                Dim parameters() As SqlParameter = {New SqlParameter("@ISSUE_NO", issueNo)}

                Dim dt As DataTable = MyBase.GetDataTable(sql, parameters)
                If dt.Rows.Count > 0 Then
                    Return dt.Rows(0)
                Else
                    Return Nothing
                End If

            Catch ex As Exception
                Throw New Exception("出庫データ取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>入出庫統計を取得</summary>
        ''' <param name="dateFrom">開始日</param>
        ''' <param name="dateTo">終了日</param>
        ''' <returns>入出庫統計データテーブル</returns>
        '''====================================================================================
        Public Function GetTransactionStatistics(dateFrom As Date, dateTo As Date) As DataTable
            Try
                Dim sql As String = "SELECT " &
                                   "(SELECT COUNT(*) FROM T_RECEIPT WHERE RECEIPT_DATE BETWEEN @DATE_FROM AND @DATE_TO) AS RECEIPT_COUNT, " &
                                   "(SELECT ISNULL(SUM(QUANTITY), 0) FROM T_RECEIPT WHERE RECEIPT_DATE BETWEEN @DATE_FROM AND @DATE_TO) AS RECEIPT_QTY, " &
                                   "(SELECT COUNT(*) FROM T_ISSUE WHERE ISSUE_DATE BETWEEN @DATE_FROM AND @DATE_TO) AS ISSUE_COUNT, " &
                                   "(SELECT ISNULL(SUM(QUANTITY), 0) FROM T_ISSUE WHERE ISSUE_DATE BETWEEN @DATE_FROM AND @DATE_TO) AS ISSUE_QTY"

                Dim parameters() As SqlParameter = {
                    New SqlParameter("@DATE_FROM", dateFrom),
                    New SqlParameter("@DATE_TO", dateTo)
                }

                Return MyBase.GetDataTable(sql, parameters)

            Catch ex As Exception
                Throw New Exception("入出庫統計取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品別入出庫統計を取得</summary>
        ''' <param name="dateFrom">開始日</param>
        ''' <param name="dateTo">終了日</param>
        ''' <returns>商品別入出庫統計データテーブル</returns>
        '''====================================================================================
        Public Function GetTransactionStatisticsByProduct(dateFrom As Date, dateTo As Date) As DataTable
            Try
                Dim sql As String = "SELECT p.PRODUCT_CD, p.PRODUCT_NAME, p.UNIT, " &
                                   "ISNULL(r.RECEIPT_QTY, 0) AS RECEIPT_QTY, " &
                                   "ISNULL(i.ISSUE_QTY, 0) AS ISSUE_QTY, " &
                                   "(ISNULL(r.RECEIPT_QTY, 0) - ISNULL(i.ISSUE_QTY, 0)) AS NET_QTY " &
                                   "FROM M_PRODUCT p " &
                                   "LEFT JOIN (SELECT PRODUCT_CD, SUM(QUANTITY) AS RECEIPT_QTY FROM T_RECEIPT WHERE RECEIPT_DATE BETWEEN @DATE_FROM AND @DATE_TO GROUP BY PRODUCT_CD) r ON p.PRODUCT_CD = r.PRODUCT_CD " &
                                   "LEFT JOIN (SELECT PRODUCT_CD, SUM(QUANTITY) AS ISSUE_QTY FROM T_ISSUE WHERE ISSUE_DATE BETWEEN @DATE_FROM AND @DATE_TO GROUP BY PRODUCT_CD) i ON p.PRODUCT_CD = i.PRODUCT_CD " &
                                   "WHERE p.ACTIVE_FLG = 1 AND (ISNULL(r.RECEIPT_QTY, 0) > 0 OR ISNULL(i.ISSUE_QTY, 0) > 0) " &
                                   "ORDER BY p.PRODUCT_CD"

                Dim parameters() As SqlParameter = {
                    New SqlParameter("@DATE_FROM", dateFrom),
                    New SqlParameter("@DATE_TO", dateTo)
                }

                Return MyBase.GetDataTable(sql, parameters)

            Catch ex As Exception
                Throw New Exception("商品別入出庫統計取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

    End Class

End Namespace
