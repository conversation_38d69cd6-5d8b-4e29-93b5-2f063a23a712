Imports System.Data
Imports System.Data.SqlClient

Namespace Stock

    '''====================================================================================
    ''' <summary>在庫データアクセスクラス</summary>
    '''====================================================================================
    Public Class StockDatabase
        Inherits Dbs.Asphalt.Database.BaseClass.DatabaseBase

        '''====================================================================================
        ''' <summary>全在庫情報を取得</summary>
        ''' <returns>在庫データテーブル</returns>
        '''====================================================================================
        Public Function SelectAll() As DataTable
            Try
                Dim sql As String = "SELECT s.PRODUCT_CD, p.PRODUCT_NAME, c.CATEGORY_NAME, " &
                                   "s.CURRENT_STOCK, s.RESERVED_STOCK, s.AVAILABLE_STOCK, " &
                                   "p.MIN_STOCK, p.UNIT, s.LAST_UPDATE, s.UPDATE_USER, " &
                                   "CASE WHEN s.AVAILABLE_STOCK <= p.MIN_STOCK THEN 1 ELSE 0 END AS LOW_STOCK_FLG " &
                                   "FROM T_STOCK s " &
                                   "INNER JOIN M_PRODUCT p ON s.PRODUCT_CD = p.PRODUCT_CD " &
                                   "INNER JOIN M_CATEGORY c ON p.CATEGORY_CD = c.CATEGORY_CD " &
                                   "WHERE p.ACTIVE_FLG = 1 " &
                                   "ORDER BY s.PRODUCT_CD"

                Return MyBase.GetDataTable(sql)

            Catch ex As Exception
                Throw New Exception("在庫一覧取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>商品コードで在庫情報を取得</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <returns>在庫データ行</returns>
        '''====================================================================================
        Public Function SelectByProductCode(productCode As String) As DataRow
            Try
                Dim sql As String = "SELECT s.PRODUCT_CD, p.PRODUCT_NAME, c.CATEGORY_NAME, " &
                                   "s.CURRENT_STOCK, s.RESERVED_STOCK, s.AVAILABLE_STOCK, " &
                                   "p.MIN_STOCK, p.UNIT, s.LAST_UPDATE, s.UPDATE_USER, " &
                                   "CASE WHEN s.AVAILABLE_STOCK <= p.MIN_STOCK THEN 1 ELSE 0 END AS LOW_STOCK_FLG " &
                                   "FROM T_STOCK s " &
                                   "INNER JOIN M_PRODUCT p ON s.PRODUCT_CD = p.PRODUCT_CD " &
                                   "INNER JOIN M_CATEGORY c ON p.CATEGORY_CD = c.CATEGORY_CD " &
                                   "WHERE s.PRODUCT_CD = @PRODUCT_CD AND p.ACTIVE_FLG = 1"

                Dim parameters As New List(Of SqlParameter)
                parameters.Add(New SqlParameter("@PRODUCT_CD", productCode))

                Dim dt As DataTable = MyBase.GetDataTable(sql, parameters.ToArray())
                If dt.Rows.Count > 0 Then
                    Return dt.Rows(0)
                Else
                    Return Nothing
                End If

            Catch ex As Exception
                Throw New Exception("在庫情報取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>在庫を検索</summary>
        ''' <param name="keyword">検索キーワード</param>
        ''' <param name="categoryCode">カテゴリコード（省略可）</param>
        ''' <param name="lowStockOnly">在庫不足のみ（省略可）</param>
        ''' <returns>在庫データテーブル</returns>
        '''====================================================================================
        Public Function Search(keyword As String, Optional categoryCode As String = "", Optional lowStockOnly As Boolean = False) As DataTable
            Try
                Dim sql As String = "SELECT s.PRODUCT_CD, p.PRODUCT_NAME, c.CATEGORY_NAME, " &
                                   "s.CURRENT_STOCK, s.RESERVED_STOCK, s.AVAILABLE_STOCK, " &
                                   "p.MIN_STOCK, p.UNIT, s.LAST_UPDATE, s.UPDATE_USER, " &
                                   "CASE WHEN s.AVAILABLE_STOCK <= p.MIN_STOCK THEN 1 ELSE 0 END AS LOW_STOCK_FLG " &
                                   "FROM T_STOCK s " &
                                   "INNER JOIN M_PRODUCT p ON s.PRODUCT_CD = p.PRODUCT_CD " &
                                   "INNER JOIN M_CATEGORY c ON p.CATEGORY_CD = c.CATEGORY_CD " &
                                   "WHERE p.ACTIVE_FLG = 1"

                Dim parameters As New List(Of SqlParameter)

                If Not String.IsNullOrEmpty(keyword) Then
                    sql &= " AND (s.PRODUCT_CD LIKE @KEYWORD OR p.PRODUCT_NAME LIKE @KEYWORD)"
                    parameters.Add(New SqlParameter("@KEYWORD", "%" & keyword & "%"))
                End If

                If Not String.IsNullOrEmpty(categoryCode) Then
                    sql &= " AND p.CATEGORY_CD = @CATEGORY_CD"
                    parameters.Add(New SqlParameter("@CATEGORY_CD", categoryCode))
                End If

                If lowStockOnly Then
                    sql &= " AND s.AVAILABLE_STOCK <= p.MIN_STOCK"
                End If

                sql &= " ORDER BY s.PRODUCT_CD"

                Return MyBase.GetDataTable(sql, parameters.ToArray())

            Catch ex As Exception
                Throw New Exception("在庫検索でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>在庫不足商品を取得</summary>
        ''' <returns>在庫不足商品データテーブル</returns>
        '''====================================================================================
        Public Function SelectLowStockProducts() As DataTable
            Try
                Dim sql As String = "SELECT s.PRODUCT_CD, p.PRODUCT_NAME, c.CATEGORY_NAME, " &
                                   "s.CURRENT_STOCK, s.RESERVED_STOCK, s.AVAILABLE_STOCK, " &
                                   "p.MIN_STOCK, p.UNIT, s.LAST_UPDATE, " &
                                   "(p.MIN_STOCK - s.AVAILABLE_STOCK) AS SHORTAGE_QTY " &
                                   "FROM T_STOCK s " &
                                   "INNER JOIN M_PRODUCT p ON s.PRODUCT_CD = p.PRODUCT_CD " &
                                   "INNER JOIN M_CATEGORY c ON p.CATEGORY_CD = c.CATEGORY_CD " &
                                   "WHERE p.ACTIVE_FLG = 1 AND s.AVAILABLE_STOCK <= p.MIN_STOCK " &
                                   "ORDER BY (p.MIN_STOCK - s.AVAILABLE_STOCK) DESC, s.PRODUCT_CD"

                Return MyBase.GetDataTable(sql)

            Catch ex As Exception
                Throw New Exception("在庫不足商品取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>在庫数を更新</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="currentStock">現在庫数</param>
        ''' <param name="reservedStock">引当済在庫数</param>
        ''' <param name="userId">ユーザーID</param>
        ''' <returns>処理結果</returns>
        '''====================================================================================
        Public Function UpdateStock(productCode As String, currentStock As Integer, reservedStock As Integer, userId As String) As Boolean
            Try
                Dim sql As String = "UPDATE T_STOCK SET CURRENT_STOCK = @CURRENT_STOCK, RESERVED_STOCK = @RESERVED_STOCK, " &
                                   "LAST_UPDATE = GETDATE(), UPDATE_USER = @USER_ID " &
                                   "WHERE PRODUCT_CD = @PRODUCT_CD"

                Dim parameters() As SqlParameter = {
                    New SqlParameter("@PRODUCT_CD", productCode),
                    New SqlParameter("@CURRENT_STOCK", currentStock),
                    New SqlParameter("@RESERVED_STOCK", reservedStock),
                    New SqlParameter("@USER_ID", userId)
                }

                Return MyBase.ExecuteNonQuery(sql, parameters) > 0

            Catch ex As Exception
                Throw New Exception("在庫更新でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>在庫データを初期化（商品登録時用）</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <param name="userId">ユーザーID</param>
        ''' <returns>処理結果</returns>
        '''====================================================================================
        Public Function InitializeStock(productCode As String, userId As String) As Boolean
            Try
                ' 既に在庫データが存在するかチェック
                If ExistsStock(productCode) Then
                    Return True ' 既に存在する場合は何もしない
                End If

                Dim sql As String = "INSERT INTO T_STOCK (PRODUCT_CD, CURRENT_STOCK, RESERVED_STOCK, UPDATE_USER) " &
                                   "VALUES (@PRODUCT_CD, 0, 0, @USER_ID)"

                Dim parameters() As SqlParameter = {
                    New SqlParameter("@PRODUCT_CD", productCode),
                    New SqlParameter("@USER_ID", userId)
                }

                Return MyBase.ExecuteNonQuery(sql, parameters) > 0

            Catch ex As Exception
                Throw New Exception("在庫初期化でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>在庫データの存在チェック</summary>
        ''' <param name="productCode">商品コード</param>
        ''' <returns>存在する場合True</returns>
        '''====================================================================================
        Public Function ExistsStock(productCode As String) As Boolean
            Try
                Dim sql As String = "SELECT COUNT(*) FROM T_STOCK WHERE PRODUCT_CD = @PRODUCT_CD"
                Dim parameters() As SqlParameter = {New SqlParameter("@PRODUCT_CD", productCode)}

                Dim count As Integer = CInt(MyBase.ExecuteScalar(sql, parameters))
                Return count > 0

            Catch ex As Exception
                Throw New Exception("在庫存在チェックでエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>在庫統計情報を取得</summary>
        ''' <returns>在庫統計データテーブル</returns>
        '''====================================================================================
        Public Function GetStockStatistics() As DataTable
            Try
                Dim sql As String = "SELECT " &
                                   "COUNT(*) AS TOTAL_PRODUCTS, " &
                                   "SUM(CASE WHEN s.AVAILABLE_STOCK <= p.MIN_STOCK THEN 1 ELSE 0 END) AS LOW_STOCK_PRODUCTS, " &
                                   "SUM(CASE WHEN s.CURRENT_STOCK = 0 THEN 1 ELSE 0 END) AS OUT_OF_STOCK_PRODUCTS, " &
                                   "SUM(s.CURRENT_STOCK) AS TOTAL_STOCK_QTY, " &
                                   "SUM(s.RESERVED_STOCK) AS TOTAL_RESERVED_QTY, " &
                                   "SUM(s.AVAILABLE_STOCK) AS TOTAL_AVAILABLE_QTY " &
                                   "FROM T_STOCK s " &
                                   "INNER JOIN M_PRODUCT p ON s.PRODUCT_CD = p.PRODUCT_CD " &
                                   "WHERE p.ACTIVE_FLG = 1"

                Return MyBase.GetDataTable(sql)

            Catch ex As Exception
                Throw New Exception("在庫統計情報取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

        '''====================================================================================
        ''' <summary>カテゴリ別在庫統計を取得</summary>
        ''' <returns>カテゴリ別在庫統計データテーブル</returns>
        '''====================================================================================
        Public Function GetStockStatisticsByCategory() As DataTable
            Try
                Dim sql As String = "SELECT c.CATEGORY_CD, c.CATEGORY_NAME, " &
                                   "COUNT(*) AS PRODUCT_COUNT, " &
                                   "SUM(s.CURRENT_STOCK) AS TOTAL_STOCK, " &
                                   "SUM(s.AVAILABLE_STOCK) AS AVAILABLE_STOCK, " &
                                   "SUM(CASE WHEN s.AVAILABLE_STOCK <= p.MIN_STOCK THEN 1 ELSE 0 END) AS LOW_STOCK_COUNT " &
                                   "FROM T_STOCK s " &
                                   "INNER JOIN M_PRODUCT p ON s.PRODUCT_CD = p.PRODUCT_CD " &
                                   "INNER JOIN M_CATEGORY c ON p.CATEGORY_CD = c.CATEGORY_CD " &
                                   "WHERE p.ACTIVE_FLG = 1 " &
                                   "GROUP BY c.CATEGORY_CD, c.CATEGORY_NAME, c.SORT_ORDER " &
                                   "ORDER BY c.SORT_ORDER, c.CATEGORY_CD"

                Return MyBase.GetDataTable(sql)

            Catch ex As Exception
                Throw New Exception("カテゴリ別在庫統計取得でエラーが発生しました: " & ex.Message, ex)
            End Try
        End Function

    End Class

End Namespace
