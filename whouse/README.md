# 在庫管理システム (Warehouse Management System)

## 概要
Asphalt Frameworkを使用した企業向け在庫管理システムです。商品の入出庫管理、在庫状況の把握、レポート機能を提供します。

## 技術仕様
- **フレームワーク**: Asphalt Framework (Dbs.Asphalt.*)
- **言語**: VB.NET
- **プラットフォーム**: ASP.NET Web Forms (.NET Framework 4.8)
- **データベース**: SQL Server
- **アーキテクチャ**: 3層アーキテクチャ

## プロジェクト構成
```
whouse/
├── Whouse.sln                          # ソリューションファイル
├── web/                                # Webアプリケーション
│   ├── Web.Config                      # Web設定ファイル
│   ├── default.aspx                    # メイン画面
│   ├── webservice.asmx                 # Webサービス
│   ├── css/                            # スタイルシート
│   ├── js/                             # JavaScript
│   ├── images/                         # 画像ファイル
│   ├── controls/                       # ユーザーコントロール
│   │   ├── Login.aspx                  # ログイン画面
│   │   ├── product/                    # 商品管理画面
│   │   ├── stock/                      # 在庫管理画面
│   │   ├── transaction/                # 入出庫管理画面
│   │   ├── report/                     # レポート画面
│   │   └── system/                     # システム管理画面
│   └── bin/                            # バイナリファイル
├── dll/                                # DLLプロジェクト
│   ├── Whouse.Application.Logic/       # ビジネスロジック層
│   │   ├── Product/                    # 商品管理ロジック
│   │   ├── Stock/                      # 在庫管理ロジック
│   │   ├── Transaction/                # 入出庫管理ロジック
│   │   ├── Report/                     # レポートロジック
│   │   └── Common/                     # 共通ロジック
│   └── Whouse.Application.Logic.Database/ # データアクセス層
│       ├── Product/                    # 商品データアクセス
│       ├── Stock/                      # 在庫データアクセス
│       ├── Transaction/                # 入出庫データアクセス
│       └── Common/                     # 共通データアクセス
└── database/                           # データベーススクリプト
    ├── create_tables.sql               # テーブル作成スクリプト
    ├── create_indexes.sql              # インデックス作成スクリプト
    └── sample_data.sql                 # サンプルデータ
```

## 主要機能
1. **認証・権限管理**
   - ユーザー認証
   - 権限レベル管理
   - セッション管理

2. **商品マスタ管理**
   - 商品登録・編集・削除
   - 商品検索
   - カテゴリ管理

3. **在庫管理**
   - 現在庫表示
   - 在庫一覧・検索
   - 在庫アラート

4. **入出庫管理**
   - 入庫登録・履歴管理
   - 出庫登録・履歴管理
   - 伝票印刷

5. **レポート機能**
   - 在庫レポート
   - 入出庫履歴レポート
   - 商品別売上レポート

## データベース設計
### 主要テーブル
- **M_PRODUCT**: 商品マスタ
- **M_CATEGORY**: カテゴリマスタ
- **T_STOCK**: 在庫テーブル
- **T_RECEIPT**: 入庫履歴
- **T_ISSUE**: 出庫履歴

## セットアップ手順
1. SQL Serverデータベースの作成
2. データベーススクリプトの実行
3. Web.Configの接続文字列設定
4. IISでのWebアプリケーション設定
5. Asphalt Frameworkライブラリの配置

## 開発環境
- Visual Studio 2019以上
- .NET Framework 4.8
- SQL Server 2016以上
- IIS 10.0以上

## ライセンス
このプロジェクトは内部使用のみを目的としています。

## 更新履歴
- 2024-08-29: プロジェクト初期作成
