/* 在庫管理システム - デフォルトスタイル */

/* 全体設定 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Meiryo UI', 'メイリオ', 'Hirag<PERSON>ku Gothic ProN', sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

/* ヘッダー */
.header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 1.8rem;
    font-weight: 600;
}

.user-info {
    font-size: 0.9rem;
}

.user-info a {
    color: #ecf0f1;
    text-decoration: none;
    margin-left: 10px;
    padding: 5px 10px;
    border: 1px solid #ecf0f1;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.user-info a:hover {
    background-color: #ecf0f1;
    color: #2c3e50;
}

/* ナビゲーション */
.navigation {
    background-color: #34495e;
    padding: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.menu {
    list-style: none;
    display: flex;
    margin: 0;
    padding: 0;
}

.menu li {
    border-right: 1px solid #2c3e50;
}

.menu li:last-child {
    border-right: none;
}

.menu a {
    display: block;
    padding: 1rem 2rem;
    color: #ecf0f1;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.menu a:hover {
    background-color: #2c3e50;
}

/* コンテンツエリア */
.content {
    padding: 2rem;
    min-height: calc(100vh - 200px);
}

/* フッター */
.footer {
    background-color: #2c3e50;
    color: #ecf0f1;
    text-align: center;
    padding: 1rem;
    margin-top: auto;
}

/* フォーム要素 */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* ボタン */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    margin: 0.25rem;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-success {
    background-color: #27ae60;
    color: white;
}

.btn-success:hover {
    background-color: #229954;
}

.btn-warning {
    background-color: #f39c12;
    color: white;
}

.btn-warning:hover {
    background-color: #e67e22;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

/* テーブル */
.table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 4px;
    overflow: hidden;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.table th {
    background-color: #34495e;
    color: white;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* カード */
.card {
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

.card-header {
    background-color: #34495e;
    color: white;
    padding: 1rem;
    font-weight: 600;
    border-radius: 4px 4px 0 0;
}

.card-body {
    padding: 1rem;
}

/* アラート */
.alert {
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    border: 1px solid transparent;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* ユーティリティクラス */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

/* レスポンシブ対応 */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        text-align: center;
    }
    
    .menu {
        flex-direction: column;
    }
    
    .menu li {
        border-right: none;
        border-bottom: 1px solid #2c3e50;
    }
    
    .content {
        padding: 1rem;
    }
}
