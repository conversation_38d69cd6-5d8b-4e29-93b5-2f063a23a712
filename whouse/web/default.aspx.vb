Imports System.Data
Imports Whouse.Application.Logic.Common

Partial Class _Default
    Inherits Dbs.Asphalt.UI.PageBase

    Private dashboardLogic As DashboardLogic

    Protected Sub Page_Load(sender As Object, e As EventArgs) Handles Me.Load
        ' ログインチェック
        If Session("LOGIN_ID") Is Nothing OrElse Session("LOGIN_ID") = "" Then
            Response.Redirect("controls/Login.aspx")
            Return
        End If

        ' ユーザー情報の表示
        lblUserInfo.Text = "ユーザー: " & Session("LOGIN_ID").ToString() & " | "

        ' ダッシュボードロジック初期化
        Dim security As New Dbs.Asphalt.Core.SystemLogic.Security("")
        security.UserID = Session("LOGIN_ID").ToString()
        dashboardLogic = New DashboardLogic(security)

        If Not IsPostBack Then
            ' ダッシュボードデータの読み込み
            LoadDashboardData()
        End If
    End Sub

    ''' <summary>
    ''' ダッシュボードデータを読み込み
    ''' </summary>
    Private Sub LoadDashboardData()
        Try
            ' 統計情報の読み込み
            LoadStatistics()

            ' 最近の取引履歴の読み込み
            LoadRecentTransactions()

            ' 在庫アラートの読み込み
            LoadStockAlerts()

            ' カテゴリ別統計の読み込み
            LoadCategoryStatistics()

            ' 人気商品の読み込み
            LoadTopProducts()

        Catch ex As Exception
            ' エラーが発生した場合はデフォルト値を表示
            SetDefaultValues()
        End Try
    End Sub

    ''' <summary>
    ''' 統計情報を読み込み
    ''' </summary>
    Private Sub LoadStatistics()
        Try
            Dim statsDt As DataTable = dashboardLogic.GetDashboardStatistics()
            If statsDt IsNot Nothing Then
                For Each row As DataRow In statsDt.Rows
                    Dim metricName As String = row("METRIC_NAME").ToString()
                    Dim metricValue As String = row("METRIC_VALUE").ToString()

                    Select Case metricName
                        Case "TOTAL_PRODUCTS"
                            lblTotalProducts.Text = metricValue
                        Case "LOW_STOCK_PRODUCTS"
                            lblLowStockProducts.Text = metricValue
                        Case "TODAY_RECEIPTS"
                            lblTodayReceipts.Text = metricValue
                        Case "TODAY_ISSUES"
                            lblTodayIssues.Text = metricValue
                    End Select
                Next
            End If
        Catch ex As Exception
            ' 統計情報読み込みエラーは無視
        End Try
    End Sub

    ''' <summary>
    ''' 最近の取引履歴を読み込み
    ''' </summary>
    Private Sub LoadRecentTransactions()
        Try
            Dim transactionsDt As DataTable = dashboardLogic.GetRecentTransactions(8)
            If transactionsDt IsNot Nothing Then
                gvRecentTransactions.DataSource = transactionsDt
                gvRecentTransactions.DataBind()
            End If
        Catch ex As Exception
            ' 取引履歴読み込みエラーは無視
        End Try
    End Sub

    ''' <summary>
    ''' 在庫アラートを読み込み
    ''' </summary>
    Private Sub LoadStockAlerts()
        Try
            Dim alertsDt As DataTable = dashboardLogic.GetStockAlerts()
            If alertsDt IsNot Nothing Then
                ' 上位5件のみ表示
                Dim topAlerts As DataTable = alertsDt.Clone()
                Dim count As Integer = Math.Min(5, alertsDt.Rows.Count)
                For i As Integer = 0 To count - 1
                    topAlerts.ImportRow(alertsDt.Rows(i))
                Next

                gvStockAlerts.DataSource = topAlerts
                gvStockAlerts.DataBind()
            End If
        Catch ex As Exception
            ' アラート読み込みエラーは無視
        End Try
    End Sub

    ''' <summary>
    ''' カテゴリ別統計を読み込み
    ''' </summary>
    Private Sub LoadCategoryStatistics()
        Try
            Dim categoryDt As DataTable = dashboardLogic.GetCategoryStockStatistics()
            If categoryDt IsNot Nothing Then
                gvCategoryStats.DataSource = categoryDt
                gvCategoryStats.DataBind()
            End If
        Catch ex As Exception
            ' カテゴリ統計読み込みエラーは無視
        End Try
    End Sub

    ''' <summary>
    ''' 人気商品を読み込み
    ''' </summary>
    Private Sub LoadTopProducts()
        Try
            Dim topProductsDt As DataTable = dashboardLogic.GetTopProducts(5, 30)
            If topProductsDt IsNot Nothing Then
                gvTopProducts.DataSource = topProductsDt
                gvTopProducts.DataBind()
            End If
        Catch ex As Exception
            ' 人気商品読み込みエラーは無視
        End Try
    End Sub

    ''' <summary>
    ''' デフォルト値を設定
    ''' </summary>
    Private Sub SetDefaultValues()
        lblTotalProducts.Text = "0"
        lblLowStockProducts.Text = "0"
        lblTodayReceipts.Text = "0"
        lblTodayIssues.Text = "0"
    End Sub

    Protected Sub lnkLogout_Click(sender As Object, e As EventArgs)
        ' セッションをクリア
        Session.Clear()
        Session.Abandon()

        ' ログイン画面にリダイレクト
        Response.Redirect("controls/Login.aspx")
    End Sub
End Class
