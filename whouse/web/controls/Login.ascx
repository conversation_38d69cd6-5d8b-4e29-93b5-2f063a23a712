<%@ Control Language="VB" AutoEventWireup="false" CodeFile="Login.ascx.vb" Inherits="Login" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<div class="login-container">
    <div class="login-header">
        <h1>在庫管理システム</h1>
        <p>システムにログインしてください</p>
    </div>
    
    <asp:Panel runat="server" ID="PanelLoginForm" CssClass="login-form">
        <asp:Panel runat="server" ID="PanelMessage" CssClass="alert alert-danger" Visible="false">
            <asp:Label ID="LabelMessage" runat="server" Text=""></asp:Label>
        </asp:Panel>
        
        <asp:Panel runat="server" ID="PanelLoginFormInner">
            <div class="form-group">
                <asp:Label ID="lblUserID" runat="server" Text="ユーザーID:" CssClass="form-label"></asp:Label>
                <cc1:TextBox runat="server" ID="TextUserID" CssClass="form-control" placeholder="ユーザーIDを入力してください"></cc1:TextBox>
            </div>
            
            <div class="form-group">
                <asp:Label ID="lblPassword" runat="server" Text="パスワード:" CssClass="form-label"></asp:Label>
                <cc1:TextBox runat="server" ID="TextPasswd" CssClass="form-control" TextMode="Password" placeholder="パスワードを入力してください"></cc1:TextBox>
            </div>
            
            <div class="form-group">
                <asp:CheckBox runat="server" ID="CheckSaveID" Text="ユーザーIDを記憶する" CssClass="form-check" />
            </div>
            
            <div class="form-group">
                <asp:Button ID="ButtonLogin" runat="server" Text="ログイン" CssClass="btn btn-primary btn-block" UseSubmitBehavior="true" />
            </div>
        </asp:Panel>
    </asp:Panel>
    
    <div class="login-info">
        <h4>デモ用アカウント:</h4>
        <ul>
            <li><strong>admin</strong> / admin123 (管理者)</li>
            <li><strong>user01</strong> / user123 (一般ユーザー)</li>
        </ul>
    </div>
</div>

<style>
.login-container {
    max-width: 400px;
    margin: 50px auto;
    padding: 2rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header h1 {
    color: #2c3e50;
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.login-header p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.login-form .form-group {
    margin-bottom: 1.5rem;
}

.login-form .form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.login-form .form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.login-form .form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.btn-block {
    width: 100%;
    padding: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
}

.login-info {
    margin-top: 2rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 0.9rem;
}

.login-info h4 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
}

.login-info ul {
    margin: 0;
    padding-left: 1.2rem;
}

.login-info li {
    margin-bottom: 0.3rem;
}

.alert {
    padding: 0.75rem;
    margin-bottom: 1rem;
    border-radius: 4px;
}

.alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}
</style>
