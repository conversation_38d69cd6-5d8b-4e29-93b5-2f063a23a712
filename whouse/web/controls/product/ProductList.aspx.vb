Imports System.Data
Imports Whouse.Application.Logic.Product

Partial Class ProductList
    Inherits System.Web.UI.Page

    Private productLogic As ProductLogic

    Protected Sub Page_Load(sender As Object, e As EventArgs) Handles Me.Load
        ' ログインチェック
        If Session("LOGIN_ID") Is Nothing OrElse Session("LOGIN_ID") = "" Then
            Response.Redirect("../Login.aspx")
            Return
        End If

        ' ユーザー情報表示
        lblUserInfo.Text = "ユーザー: " & Session("LOGIN_ID").ToString() & " | "

        ' ロジッククラス初期化
        Dim security As New Dbs.Asphalt.Core.SystemLogic.Security("")
        security.UserID = Session("LOGIN_ID").ToString()
        productLogic = New ProductLogic(security)

        If Not IsPostBack Then
            ' カテゴリドロップダウンの初期化
            LoadCategoryDropDown()
            
            ' 商品一覧の初期表示
            LoadProductList()
        End If
    End Sub

    ''' <summary>
    ''' カテゴリドロップダウンを読み込み
    ''' </summary>
    Private Sub LoadCategoryDropDown()
        Try
            Dim categoryDt As DataTable = productLogic.GetCategoryDropDownList()
            If categoryDt IsNot Nothing Then
                ddlCategory.DataSource = categoryDt
                ddlCategory.DataTextField = "CATEGORY_NAME"
                ddlCategory.DataValueField = "CATEGORY_CD"
                ddlCategory.DataBind()
                
                ' 先頭に「全て」を追加
                ddlCategory.Items.Insert(0, New ListItem("-- 全て --", ""))
            End If
        Catch ex As Exception
            ShowError("カテゴリ読み込みでエラーが発生しました: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' 商品一覧を読み込み
    ''' </summary>
    Private Sub LoadProductList()
        Try
            Dim productDt As DataTable

            ' 検索条件がある場合は検索、ない場合は全件取得
            If Not String.IsNullOrEmpty(txtKeyword.Text) OrElse Not String.IsNullOrEmpty(ddlCategory.SelectedValue) Then
                productDt = productLogic.SearchProducts(txtKeyword.Text.Trim(), ddlCategory.SelectedValue)
            Else
                productDt = productLogic.GetProductList()
            End If

            If productDt IsNot Nothing Then
                gvProducts.DataSource = productDt
                gvProducts.DataBind()
                
                ' 件数表示
                lblRecordCount.Text = $"検索結果: {productDt.Rows.Count} 件"
            Else
                ShowError(productLogic.LastError)
            End If

        Catch ex As Exception
            ShowError("商品一覧読み込みでエラーが発生しました: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' 検索ボタンクリック
    ''' </summary>
    Protected Sub btnSearch_Click(sender As Object, e As EventArgs)
        gvProducts.PageIndex = 0 ' ページを先頭に戻す
        LoadProductList()
    End Sub

    ''' <summary>
    ''' クリアボタンクリック
    ''' </summary>
    Protected Sub btnClear_Click(sender As Object, e As EventArgs)
        txtKeyword.Text = ""
        ddlCategory.SelectedIndex = 0
        gvProducts.PageIndex = 0
        LoadProductList()
    End Sub

    ''' <summary>
    ''' 新規登録ボタンクリック
    ''' </summary>
    Protected Sub btnNew_Click(sender As Object, e As EventArgs)
        Response.Redirect("ProductEdit.aspx?mode=new")
    End Sub

    ''' <summary>
    ''' GridViewページング
    ''' </summary>
    Protected Sub gvProducts_PageIndexChanging(sender As Object, e As GridViewPageEventArgs)
        gvProducts.PageIndex = e.NewPageIndex
        LoadProductList()
    End Sub

    ''' <summary>
    ''' GridView行コマンド
    ''' </summary>
    Protected Sub gvProducts_RowCommand(sender As Object, e As GridViewCommandEventArgs)
        Dim productCode As String = e.CommandArgument.ToString()

        Select Case e.CommandName
            Case "EditProduct"
                ' 編集画面に遷移
                Response.Redirect($"ProductEdit.aspx?mode=edit&code={Server.UrlEncode(productCode)}")

            Case "DeleteProduct"
                ' 商品削除
                DeleteProduct(productCode)
        End Select
    End Sub

    ''' <summary>
    ''' 商品削除処理
    ''' </summary>
    ''' <param name="productCode">商品コード</param>
    Private Sub DeleteProduct(productCode As String)
        Try
            If productLogic.DeleteProduct(productCode) Then
                ShowMessage(productLogic.LastMessage)
                LoadProductList() ' 一覧を再読み込み
            Else
                ShowError(productLogic.LastError)
            End If

        Catch ex As Exception
            ShowError("商品削除でエラーが発生しました: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' ログアウトボタンクリック
    ''' </summary>
    Protected Sub lnkLogout_Click(sender As Object, e As EventArgs)
        Session.Clear()
        Session.Abandon()
        Response.Redirect("../Login.aspx")
    End Sub

    ''' <summary>
    ''' 成功メッセージ表示
    ''' </summary>
    ''' <param name="message">メッセージ</param>
    Private Sub ShowMessage(message As String)
        lblMessage.Text = message
        pnlMessage.Visible = True
        pnlError.Visible = False
    End Sub

    ''' <summary>
    ''' エラーメッセージ表示
    ''' </summary>
    ''' <param name="message">エラーメッセージ</param>
    Private Sub ShowError(message As String)
        lblError.Text = message
        pnlError.Visible = True
        pnlMessage.Visible = False
    End Sub

End Class
