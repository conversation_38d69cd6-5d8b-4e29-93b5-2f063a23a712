<%@ Page Language="VB" AutoEventWireup="false" CodeFile="ProductList.aspx.vb" Inherits="ProductList" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>商品一覧 - 在庫管理システム</title>
    <link href="../../css/default.css" rel="stylesheet" type="text/css" />
</head>
<body>
    <form id="form1" runat="server">
        <div class="header">
            <h1>在庫管理システム</h1>
            <div class="user-info">
                <asp:Label ID="lblUserInfo" runat="server"></asp:Label>
                <asp:LinkButton ID="lnkLogout" runat="server" Text="ログアウト" OnClick="lnkLogout_Click"></asp:LinkButton>
            </div>
        </div>
        
        <div class="navigation">
            <ul class="menu">
                <li><a href="../product/ProductList.aspx" class="active">商品管理</a></li>
                <li><a href="../stock/StockList.aspx">在庫管理</a></li>
                <li><a href="../transaction/TransactionMenu.aspx">入出庫管理</a></li>
                <li><a href="../report/ReportMenu.aspx">レポート</a></li>
                <li><a href="../system/SystemMenu.aspx">システム管理</a></li>
            </ul>
        </div>
        
        <div class="content">
            <div class="card">
                <div class="card-header">
                    <h2>商品一覧</h2>
                </div>
                <div class="card-body">
                    <!-- 検索エリア -->
                    <div class="search-area mb-3">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">商品名・商品コード:</label>
                                <cc1:TextBox ID="txtKeyword" runat="server" CssClass="form-control" placeholder="検索キーワードを入力"></cc1:TextBox>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">カテゴリ:</label>
                                <asp:DropDownList ID="ddlCategory" runat="server" CssClass="form-control">
                                    <asp:ListItem Value="" Text="-- 全て --"></asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <asp:Button ID="btnSearch" runat="server" Text="検索" CssClass="btn btn-primary" OnClick="btnSearch_Click" />
                                    <asp:Button ID="btnClear" runat="server" Text="クリア" CssClass="btn btn-secondary" OnClick="btnClear_Click" />
                                </div>
                            </div>
                            <div class="col-md-3 text-right">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <asp:Button ID="btnNew" runat="server" Text="新規登録" CssClass="btn btn-success" OnClick="btnNew_Click" />
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- メッセージエリア -->
                    <asp:Panel ID="pnlMessage" runat="server" Visible="false">
                        <div class="alert alert-info">
                            <asp:Label ID="lblMessage" runat="server"></asp:Label>
                        </div>
                    </asp:Panel>
                    
                    <asp:Panel ID="pnlError" runat="server" Visible="false">
                        <div class="alert alert-danger">
                            <asp:Label ID="lblError" runat="server"></asp:Label>
                        </div>
                    </asp:Panel>
                    
                    <!-- 商品一覧テーブル -->
                    <div class="table-responsive">
                        <asp:GridView ID="gvProducts" runat="server" CssClass="table table-striped" 
                                      AutoGenerateColumns="false" AllowPaging="true" PageSize="20"
                                      OnPageIndexChanging="gvProducts_PageIndexChanging"
                                      OnRowCommand="gvProducts_RowCommand"
                                      EmptyDataText="商品データがありません。">
                            <Columns>
                                <asp:BoundField DataField="PRODUCT_CD" HeaderText="商品コード" />
                                <asp:BoundField DataField="PRODUCT_NAME" HeaderText="商品名" />
                                <asp:BoundField DataField="CATEGORY_NAME" HeaderText="カテゴリ" />
                                <asp:BoundField DataField="UNIT_PRICE" HeaderText="単価" DataFormatString="{0:N0}" ItemStyle-HorizontalAlign="Right" />
                                <asp:BoundField DataField="MIN_STOCK" HeaderText="最小在庫数" DataFormatString="{0:N0}" ItemStyle-HorizontalAlign="Right" />
                                <asp:BoundField DataField="UNIT" HeaderText="単位" />
                                <asp:BoundField DataField="UPDATE_DATE" HeaderText="更新日時" DataFormatString="{0:yyyy/MM/dd HH:mm}" />
                                <asp:TemplateField HeaderText="操作" ItemStyle-Width="150px" ItemStyle-HorizontalAlign="Center">
                                    <ItemTemplate>
                                        <asp:Button ID="btnEdit" runat="server" Text="編集" CssClass="btn btn-sm btn-primary"
                                                    CommandName="EditProduct" CommandArgument='<%# Eval("PRODUCT_CD") %>' />
                                        <asp:Button ID="btnDelete" runat="server" Text="削除" CssClass="btn btn-sm btn-danger"
                                                    CommandName="DeleteProduct" CommandArgument='<%# Eval("PRODUCT_CD") %>'
                                                    OnClientClick="return confirm('この商品を削除してもよろしいですか？');" />
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                            <PagerStyle CssClass="pagination-wrapper" />
                        </asp:GridView>
                    </div>
                    
                    <!-- 件数表示 -->
                    <div class="mt-3">
                        <asp:Label ID="lblRecordCount" runat="server" CssClass="text-muted"></asp:Label>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; 2024 在庫管理システム - Powered by Asphalt Framework</p>
        </div>
    </form>
</body>
</html>

<style>
.search-area {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: -0.5rem;
}

.col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
    padding: 0.5rem;
}

.text-right {
    text-align: right;
}

.table-responsive {
    overflow-x: auto;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    margin: 0 2px;
}

.pagination-wrapper {
    text-align: center;
    margin-top: 1rem;
}

.menu .active {
    background-color: #2c3e50;
    font-weight: bold;
}

@media (max-width: 768px) {
    .col-md-3 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .text-right {
        text-align: left;
    }
}
</style>
