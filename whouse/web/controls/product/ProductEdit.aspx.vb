Imports System.Data
Imports Whouse.Application.Logic.Product

Partial Class ProductEdit
    Inherits System.Web.UI.Page

    Private productLogic As ProductLogic
    Private editMode As String = "new"
    Private originalProductCode As String = ""

    Protected Sub Page_Load(sender As Object, e As EventArgs) Handles Me.Load
        ' ログインチェック
        If Session("LOGIN_ID") Is Nothing OrElse Session("LOGIN_ID") = "" Then
            Response.Redirect("../Login.aspx")
            Return
        End If

        ' ユーザー情報表示
        lblUserInfo.Text = "ユーザー: " & Session("LOGIN_ID").ToString() & " | "

        ' ロジッククラス初期化
        Dim security As New Dbs.Asphalt.Core.SystemLogic.Security("")
        security.UserID = Session("LOGIN_ID").ToString()
        productLogic = New ProductLogic(security)

        ' モード判定
        editMode = If(Request.QueryString("mode"), "new")
        originalProductCode = If(Request.QueryString("code"), "")

        If Not IsPostBack Then
            ' カテゴリドロップダウンの初期化
            LoadCategoryDropDown()
            
            ' モードに応じた初期化
            InitializeForm()
        End If
    End Sub

    ''' <summary>
    ''' フォーム初期化
    ''' </summary>
    Private Sub InitializeForm()
        Select Case editMode.ToLower()
            Case "new"
                ' 新規登録モード
                lblTitle.Text = "商品登録"
                txtProductCode.ReadOnly = False
                
            Case "edit"
                ' 編集モード
                lblTitle.Text = "商品編集"
                txtProductCode.ReadOnly = True
                LoadProductData()
                
            Case Else
                ' 不正なモード
                Response.Redirect("ProductList.aspx")
        End Select
    End Sub

    ''' <summary>
    ''' カテゴリドロップダウンを読み込み
    ''' </summary>
    Private Sub LoadCategoryDropDown()
        Try
            Dim categoryDt As DataTable = productLogic.GetCategoryDropDownList()
            If categoryDt IsNot Nothing Then
                ddlCategory.DataSource = categoryDt
                ddlCategory.DataTextField = "CATEGORY_NAME"
                ddlCategory.DataValueField = "CATEGORY_CD"
                ddlCategory.DataBind()
                
                ' 先頭に選択項目を追加
                ddlCategory.Items.Insert(0, New ListItem("-- 選択してください --", ""))
            End If
        Catch ex As Exception
            ShowError("カテゴリ読み込みでエラーが発生しました: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' 商品データを読み込み（編集モード用）
    ''' </summary>
    Private Sub LoadProductData()
        Try
            If String.IsNullOrEmpty(originalProductCode) Then
                ShowError("商品コードが指定されていません。")
                Return
            End If

            Dim productRow As DataRow = productLogic.GetProduct(originalProductCode)
            If productRow IsNot Nothing Then
                txtProductCode.Text = productRow("PRODUCT_CD").ToString()
                txtProductName.Text = productRow("PRODUCT_NAME").ToString()
                ddlCategory.SelectedValue = productRow("CATEGORY_CD").ToString()
                txtUnitPrice.Text = productRow("UNIT_PRICE").ToString()
                txtMinStock.Text = productRow("MIN_STOCK").ToString()
                txtUnit.Text = productRow("UNIT").ToString()
                txtDescription.Text = If(IsDBNull(productRow("DESCRIPTION")), "", productRow("DESCRIPTION").ToString())
            Else
                ShowError("指定された商品が見つかりません。")
            End If

        Catch ex As Exception
            ShowError("商品データ読み込みでエラーが発生しました: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' 商品コード自動生成ボタンクリック
    ''' </summary>
    Protected Sub btnGenerateCode_Click(sender As Object, e As EventArgs)
        If editMode = "new" Then
            Try
                Dim nextCode As String = productLogic.GenerateNextProductCode()
                If Not String.IsNullOrEmpty(nextCode) Then
                    txtProductCode.Text = nextCode
                Else
                    ShowError(productLogic.LastError)
                End If
            Catch ex As Exception
                ShowError("商品コード生成でエラーが発生しました: " & ex.Message)
            End Try
        End If
    End Sub

    ''' <summary>
    ''' 保存ボタンクリック
    ''' </summary>
    Protected Sub btnSave_Click(sender As Object, e As EventArgs)
        Try
            ' 入力値取得
            Dim productCode As String = txtProductCode.Text.Trim()
            Dim productName As String = txtProductName.Text.Trim()
            Dim categoryCode As String = ddlCategory.SelectedValue
            Dim unitPrice As Decimal
            Dim minStock As Integer
            Dim unit As String = txtUnit.Text.Trim()
            Dim description As String = txtDescription.Text.Trim()

            ' 数値変換チェック
            If Not Decimal.TryParse(txtUnitPrice.Text, unitPrice) Then
                ShowError("単価は数値で入力してください。")
                txtUnitPrice.Focus()
                Return
            End If

            If Not Integer.TryParse(txtMinStock.Text, minStock) Then
                ShowError("最小在庫数は整数で入力してください。")
                txtMinStock.Focus()
                Return
            End If

            ' 保存処理
            Dim result As Boolean = False
            Select Case editMode.ToLower()
                Case "new"
                    result = productLogic.RegisterProduct(productCode, productName, categoryCode, unitPrice, minStock, unit, description)
                Case "edit"
                    result = productLogic.UpdateProduct(productCode, productName, categoryCode, unitPrice, minStock, unit, description)
            End Select

            If result Then
                ShowMessage(productLogic.LastMessage)
                
                ' 新規登録の場合はフォームをクリア
                If editMode = "new" Then
                    ClearForm()
                End If
            Else
                ShowError(productLogic.LastError)
            End If

        Catch ex As Exception
            ShowError("保存処理でエラーが発生しました: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' キャンセルボタンクリック
    ''' </summary>
    Protected Sub btnCancel_Click(sender As Object, e As EventArgs)
        If editMode = "new" Then
            ClearForm()
        Else
            LoadProductData() ' 元のデータを再読み込み
        End If
        HideMessages()
    End Sub

    ''' <summary>
    ''' 一覧に戻るボタンクリック
    ''' </summary>
    Protected Sub btnBack_Click(sender As Object, e As EventArgs)
        Response.Redirect("ProductList.aspx")
    End Sub

    ''' <summary>
    ''' ログアウトボタンクリック
    ''' </summary>
    Protected Sub lnkLogout_Click(sender As Object, e As EventArgs)
        Session.Clear()
        Session.Abandon()
        Response.Redirect("../Login.aspx")
    End Sub

    ''' <summary>
    ''' フォームクリア
    ''' </summary>
    Private Sub ClearForm()
        txtProductCode.Text = ""
        txtProductName.Text = ""
        ddlCategory.SelectedIndex = 0
        txtUnitPrice.Text = "0"
        txtMinStock.Text = "0"
        txtUnit.Text = "個"
        txtDescription.Text = ""
    End Sub

    ''' <summary>
    ''' 成功メッセージ表示
    ''' </summary>
    ''' <param name="message">メッセージ</param>
    Private Sub ShowMessage(message As String)
        lblMessage.Text = message
        pnlMessage.Visible = True
        pnlError.Visible = False
    End Sub

    ''' <summary>
    ''' エラーメッセージ表示
    ''' </summary>
    ''' <param name="message">エラーメッセージ</param>
    Private Sub ShowError(message As String)
        lblError.Text = message
        pnlError.Visible = True
        pnlMessage.Visible = False
    End Sub

    ''' <summary>
    ''' メッセージ非表示
    ''' </summary>
    Private Sub HideMessages()
        pnlMessage.Visible = False
        pnlError.Visible = False
    End Sub

End Class
