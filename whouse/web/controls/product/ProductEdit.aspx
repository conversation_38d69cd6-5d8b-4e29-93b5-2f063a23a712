<%@ Page Language="VB" AutoEventWireup="false" CodeFile="ProductEdit.aspx.vb" Inherits="ProductEdit" %>
    <%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

        <!DOCTYPE html>
        <html xmlns="http://www.w3.org/1999/xhtml">

        <head runat="server">
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
            <title>商品編集 - 在庫管理システム</title>
            <link href="../../css/default.css" rel="stylesheet" type="text/css" />
        </head>

        <body>
            <form id="form1" runat="server">
                <div class="header">
                    <h1>在庫管理システム</h1>
                    <div class="user-info">
                        <asp:Label ID="lblUserInfo" runat="server"></asp:Label>
                        <asp:LinkButton ID="lnkLogout" runat="server" Text="ログアウト" OnClick="lnkLogout_Click">
                        </asp:LinkButton>
                    </div>
                </div>

                <div class="navigation">
                    <ul class="menu">
                        <li><a href="../product/ProductList.aspx" class="active">商品管理</a></li>
                        <li><a href="../stock/StockList.aspx">在庫管理</a></li>
                        <li><a href="../transaction/TransactionMenu.aspx">入出庫管理</a></li>
                        <li><a href="../report/ReportMenu.aspx">レポート</a></li>
                        <li><a href="../system/SystemMenu.aspx">システム管理</a></li>
                    </ul>
                </div>

                <div class="content">
                    <div class="card">
                        <div class="card-header">
                            <h2>
                                <asp:Label ID="lblTitle" runat="server" Text="商品登録"></asp:Label>
                            </h2>
                        </div>
                        <div class="card-body">
                            <!-- メッセージエリア -->
                            <asp:Panel ID="pnlMessage" runat="server" Visible="false">
                                <div class="alert alert-success">
                                    <asp:Label ID="lblMessage" runat="server"></asp:Label>
                                </div>
                            </asp:Panel>

                            <asp:Panel ID="pnlError" runat="server" Visible="false">
                                <div class="alert alert-danger">
                                    <asp:Label ID="lblError" runat="server"></asp:Label>
                                </div>
                            </asp:Panel>

                            <!-- 商品情報入力フォーム -->
                            <div class="form-container">
                                <div class="form-row">
                                    <div class="form-group col-md-6">
                                        <label class="form-label required">商品コード:</label>
                                        <cc1:TextBox ID="txtProductCode" runat="server" CssClass="form-control"
                                            MaxLength="20"></cc1:TextBox>
                                        <asp:Button ID="btnGenerateCode" runat="server" Text="自動生成"
                                            CssClass="btn btn-sm btn-secondary mt-1" OnClick="btnGenerateCode_Click" />
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="form-label required">商品名:</label>
                                        <cc1:TextBox ID="txtProductName" runat="server" CssClass="form-control"
                                            MaxLength="100"></cc1:TextBox>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group col-md-6">
                                        <label class="form-label required">カテゴリ:</label>
                                        <asp:DropDownList ID="ddlCategory" runat="server" CssClass="form-control">
                                            <asp:ListItem Value="" Text="-- 選択してください --"></asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="form-label required">単位:</label>
                                        <cc1:TextBox ID="txtUnit" runat="server" CssClass="form-control" MaxLength="10"
                                            Text="個"></cc1:TextBox>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group col-md-6">
                                        <label class="form-label">単価:</label>
                                        <cc1:TextBox ID="txtUnitPrice" runat="server" CssClass="form-control" Text="0">
                                        </cc1:TextBox>
                                        <small class="form-text text-muted">円</small>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="form-label">最小在庫数:</label>
                                        <cc1:TextBox ID="txtMinStock" runat="server" CssClass="form-control" Text="0">
                                        </cc1:TextBox>
                                        <small class="form-text text-muted">この数量を下回るとアラートが表示されます</small>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group col-md-12">
                                        <label class="form-label">説明:</label>
                                        <cc1:TextBox ID="txtDescription" runat="server" CssClass="form-control"
                                            TextMode="MultiLine" Rows="3" MaxLength="500"></cc1:TextBox>
                                        <small class="form-text text-muted">最大500文字</small>
                                    </div>
                                </div>

                                <!-- ボタンエリア -->
                                <div class="button-area mt-4">
                                    <asp:Button ID="btnSave" runat="server" Text="保存" CssClass="btn btn-primary"
                                        OnClick="btnSave_Click" />
                                    <asp:Button ID="btnCancel" runat="server" Text="キャンセル" CssClass="btn btn-secondary"
                                        OnClick="btnCancel_Click" CausesValidation="false" />
                                    <asp:Button ID="btnBack" runat="server" Text="一覧に戻る"
                                        CssClass="btn btn-outline-secondary" OnClick="btnBack_Click"
                                        CausesValidation="false" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="footer">
                    <p>&copy; 2024 在庫管理システム - Powered by Asphalt Framework</p>
                </div>
            </form>
        </body>

        </html>

        <style>
            .form-container {
                max-width: 800px;
            }

            .form-row {
                display: flex;
                flex-wrap: wrap;
                margin: -0.5rem;
            }

            .form-group {
                padding: 0.5rem;
                margin-bottom: 1rem;
            }

            .col-md-6 {
                flex: 0 0 50%;
                max-width: 50%;
            }

            .col-md-12 {
                flex: 0 0 100%;
                max-width: 100%;
            }

            .form-label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 600;
                color: #2c3e50;
            }

            .form-label.required::after {
                content: " *";
                color: #e74c3c;
            }

            .form-control {
                width: 100%;
                padding: 0.75rem;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                transition: border-color 0.3s ease;
            }

            .form-control:focus {
                outline: none;
                border-color: #3498db;
                box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
            }

            .form-text {
                font-size: 0.875rem;
                margin-top: 0.25rem;
            }

            .text-muted {
                color: #6c757d;
            }

            .button-area {
                text-align: center;
                padding-top: 2rem;
                border-top: 1px solid #dee2e6;
            }

            .button-area .btn {
                margin: 0 0.5rem;
            }

            .btn-outline-secondary {
                color: #6c757d;
                border-color: #6c757d;
                background-color: transparent;
            }

            .btn-outline-secondary:hover {
                color: #fff;
                background-color: #6c757d;
                border-color: #6c757d;
            }

            .mt-1 {
                margin-top: 0.25rem;
            }

            .mt-4 {
                margin-top: 1.5rem;
            }

            @media (max-width: 768px) {

                .col-md-6,
                .col-md-12 {
                    flex: 0 0 100%;
                    max-width: 100%;
                }

                .button-area .btn {
                    display: block;
                    width: 100%;
                    margin: 0.25rem 0;
                }
            }
        </style>