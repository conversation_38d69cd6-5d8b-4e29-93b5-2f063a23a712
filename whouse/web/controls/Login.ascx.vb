Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.UI
Imports System.Data.SqlClient

Partial Class Login
    Inherits ModuleBase

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return "ユーザー認証"
        End Get
    End Property

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return Security.emUserAuth.一般
        End Get
    End Property

    Protected Overrides Sub OnInit(e As System.EventArgs)
        ' 既にログイン済みの場合はメイン画面にリダイレクト
        If Not IsNothing(Session("LOGIN_ID")) And Session("LOGIN_ID") <> "" Then
            Response.Redirect("~/default.aspx")
            Return
        End If

        ' 保存されたユーザーIDを復元
        Try
            If Request.Cookies("Whouse.UserID") IsNot Nothing Then
                TextUserID.Text = Request.Cookies("Whouse.UserID").Value
                CheckSaveID.Checked = True
            End If
        Catch ex As Exception
            ' Cookie読み込みエラーは無視
        End Try

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)
        If Not IsPostBack Then
            ' フォーカス設定
            If TextUserID.Text <> "" Then
                TextPasswd.Focus()
            Else
                TextUserID.Focus()
            End If
        End If

        MyBase.OnLoad(e)
    End Sub

    Protected Sub ButtonLogin_Click(sender As Object, e As System.EventArgs) Handles ButtonLogin.Click
        Try
            ' 入力値検証
            If String.IsNullOrEmpty(TextUserID.Text.Trim()) Then
                ShowError("ユーザーIDを入力してください。")
                TextUserID.Focus()
                Return
            End If

            If String.IsNullOrEmpty(TextPasswd.Text) Then
                ShowError("パスワードを入力してください。")
                TextPasswd.Focus()
                Return
            End If

            ' ユーザー認証
            If AuthenticateUser(TextUserID.Text.Trim(), TextPasswd.Text) Then
                ' 認証成功
                Session("LOGIN_ID") = TextUserID.Text.Trim()
                Session("LOGIN_TIME") = DateTime.Now

                ' ユーザーIDの保存設定
                If CheckSaveID.Checked Then
                    Dim cookie As New HttpCookie("Whouse.UserID", TextUserID.Text.Trim())
                    cookie.Expires = DateTime.Now.AddDays(30)
                    Response.Cookies.Add(cookie)
                Else
                    ' Cookieを削除
                    If Request.Cookies("Whouse.UserID") IsNot Nothing Then
                        Dim cookie As New HttpCookie("Whouse.UserID", "")
                        cookie.Expires = DateTime.Now.AddDays(-1)
                        Response.Cookies.Add(cookie)
                    End If
                End If

                ' ログイン履歴の記録
                RecordLoginHistory(TextUserID.Text.Trim(), True, "")

                ' メイン画面にリダイレクト
                Response.Redirect("~/default.aspx")
            Else
                ' 認証失敗
                ShowError("ユーザーIDまたはパスワードが正しくありません。")
                RecordLoginHistory(TextUserID.Text.Trim(), False, "認証失敗")
                TextPasswd.Text = ""
                TextUserID.Focus()
            End If

        Catch ex As Exception
            ShowError("ログイン処理中にエラーが発生しました: " & ex.Message)
            RecordLoginHistory(TextUserID.Text.Trim(), False, "システムエラー: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' ユーザー認証を行う
    ''' </summary>
    ''' <param name="userID">ユーザーID</param>
    ''' <param name="password">パスワード</param>
    ''' <returns>認証結果</returns>
    Private Function AuthenticateUser(userID As String, password As String) As Boolean
        Try
            Using conn As New SqlConnection(Base.Security.ConnectionString)
                conn.Open()

                Dim sql As String = "SELECT USER_ID, USER_NAME, USER_ROLE, ACTIVE_FLG FROM M_USER WHERE USER_ID = @USER_ID AND PASSWORD = @PASSWORD"
                Using cmd As New SqlCommand(sql, conn)
                    cmd.Parameters.AddWithValue("@USER_ID", userID)
                    cmd.Parameters.AddWithValue("@PASSWORD", password) ' 実際の運用ではハッシュ化が必要

                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        If reader.Read() Then
                            ' ユーザーが見つかった場合
                            If Not reader.GetBoolean("ACTIVE_FLG") Then
                                ShowError("このユーザーは無効化されています。")
                                Return False
                            End If

                            ' セッションにユーザー情報を保存
                            Session("USER_NAME") = reader.GetString("USER_NAME")
                            Session("USER_ROLE") = reader.GetString("USER_ROLE")

                            ' 最終ログイン日時を更新
                            UpdateLastLogin(userID)

                            Return True
                        End If
                    End Using
                End Using
            End Using

            Return False

        Catch ex As Exception
            Throw New Exception("ユーザー認証処理でエラーが発生しました: " & ex.Message, ex)
        End Try
    End Function

    ''' <summary>
    ''' 最終ログイン日時を更新
    ''' </summary>
    ''' <param name="userID">ユーザーID</param>
    Private Sub UpdateLastLogin(userID As String)
        Try
            Using conn As New SqlConnection(Base.Security.ConnectionString)
                conn.Open()

                Dim sql As String = "UPDATE M_USER SET LAST_LOGIN = GETDATE(), UPDATE_DATE = GETDATE(), UPDATE_USER = @USER_ID WHERE USER_ID = @USER_ID"
                Using cmd As New SqlCommand(sql, conn)
                    cmd.Parameters.AddWithValue("@USER_ID", userID)
                    cmd.ExecuteNonQuery()
                End Using
            End Using
        Catch ex As Exception
            ' ログイン日時更新エラーは無視（ログインは継続）
        End Try
    End Sub

    ''' <summary>
    ''' ログイン履歴を記録
    ''' </summary>
    ''' <param name="userID">ユーザーID</param>
    ''' <param name="success">成功フラグ</param>
    ''' <param name="remarks">備考</param>
    Private Sub RecordLoginHistory(userID As String, success As Boolean, remarks As String)
        Try
            Using conn As New SqlConnection(Base.Security.ConnectionString)
                conn.Open()

                Dim actionType As String = If(success, "LOGIN_SUCCESS", "LOGIN_FAILED")
                Dim sql As String = "INSERT INTO T_SYSTEM_LOG (USER_ID, ACTION_TYPE, TABLE_NAME, RECORD_KEY, NEW_VALUE, IP_ADDRESS, USER_AGENT) " &
                                   "VALUES (@USER_ID, @ACTION_TYPE, 'M_USER', @USER_ID, @REMARKS, @IP_ADDRESS, @USER_AGENT)"

                Using cmd As New SqlCommand(sql, conn)
                    cmd.Parameters.AddWithValue("@USER_ID", userID)
                    cmd.Parameters.AddWithValue("@ACTION_TYPE", actionType)
                    cmd.Parameters.AddWithValue("@REMARKS", remarks)
                    cmd.Parameters.AddWithValue("@IP_ADDRESS", Request.ServerVariables("REMOTE_ADDR"))
                    cmd.Parameters.AddWithValue("@USER_AGENT", Request.ServerVariables("HTTP_USER_AGENT"))
                    cmd.ExecuteNonQuery()
                End Using
            End Using
        Catch ex As Exception
            ' ログ記録エラーは無視
        End Try
    End Sub

    ''' <summary>
    ''' エラーメッセージを表示
    ''' </summary>
    ''' <param name="message">エラーメッセージ</param>
    Private Sub ShowError(message As String)
        LabelMessage.Text = message
        PanelMessage.Visible = True
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)
        ' 実装不要
    End Sub

    Public Overrides Sub FinderOpen()
        ' 実装不要
    End Sub

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As ModuleBase.MessageBoxResult)
        ' 実装不要
    End Sub
End Class
