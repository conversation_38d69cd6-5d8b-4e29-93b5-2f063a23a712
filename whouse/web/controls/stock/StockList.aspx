<%@ Page Language="VB" AutoEventWireup="false" CodeFile="StockList.aspx.vb" Inherits="StockList" %>
    <%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

        <!DOCTYPE html>
        <html xmlns="http://www.w3.org/1999/xhtml">

        <head runat="server">
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
            <title>在庫一覧 - 在庫管理システム</title>
            <link href="../../css/default.css" rel="stylesheet" type="text/css" />
        </head>

        <body>
            <form id="form1" runat="server">
                <div class="header">
                    <h1>在庫管理システム</h1>
                    <div class="user-info">
                        <asp:Label ID="lblUserInfo" runat="server"></asp:Label>
                        <asp:LinkButton ID="lnkLogout" runat="server" Text="ログアウト" OnClick="lnkLogout_Click">
                        </asp:LinkButton>
                    </div>
                </div>

                <div class="navigation">
                    <ul class="menu">
                        <li><a href="../product/ProductList.aspx">商品管理</a></li>
                        <li><a href="../stock/StockList.aspx" class="active">在庫管理</a></li>
                        <li><a href="../transaction/TransactionMenu.aspx">入出庫管理</a></li>
                        <li><a href="../report/ReportMenu.aspx">レポート</a></li>
                        <li><a href="../system/SystemMenu.aspx">システム管理</a></li>
                    </ul>
                </div>

                <div class="content">
                    <!-- 在庫統計カード -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-icon">📦</div>
                                <div class="stat-content">
                                    <div class="stat-number">
                                        <asp:Label ID="lblTotalProducts" runat="server" Text="0"></asp:Label>
                                    </div>
                                    <div class="stat-label">総商品数</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card alert-warning">
                                <div class="stat-icon">⚠️</div>
                                <div class="stat-content">
                                    <div class="stat-number">
                                        <asp:Label ID="lblLowStockProducts" runat="server" Text="0"></asp:Label>
                                    </div>
                                    <div class="stat-label">在庫不足</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card alert-danger">
                                <div class="stat-icon">❌</div>
                                <div class="stat-content">
                                    <div class="stat-number">
                                        <asp:Label ID="lblOutOfStockProducts" runat="server" Text="0"></asp:Label>
                                    </div>
                                    <div class="stat-label">在庫切れ</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card alert-info">
                                <div class="stat-icon">📊</div>
                                <div class="stat-content">
                                    <div class="stat-number">
                                        <asp:Label ID="lblTotalStockQty" runat="server" Text="0"></asp:Label>
                                    </div>
                                    <div class="stat-label">総在庫数</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h2>在庫一覧</h2>
                        </div>
                        <div class="card-body">
                            <!-- 検索エリア -->
                            <div class="search-area mb-3">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label class="form-label">商品名・商品コード:</label>
                                        <cc1:TextBox ID="txtKeyword" runat="server" CssClass="form-control"
                                            placeholder="検索キーワードを入力"></cc1:TextBox>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">カテゴリ:</label>
                                        <asp:DropDownList ID="ddlCategory" runat="server" CssClass="form-control">
                                            <asp:ListItem Value="" Text="-- 全て --"></asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">表示条件:</label>
                                        <asp:CheckBox ID="chkLowStockOnly" runat="server" Text="在庫不足のみ"
                                            CssClass="form-check" />
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">&nbsp;</label>
                                        <div>
                                            <asp:Button ID="btnSearch" runat="server" Text="検索"
                                                CssClass="btn btn-primary" OnClick="btnSearch_Click" />
                                            <asp:Button ID="btnClear" runat="server" Text="クリア"
                                                CssClass="btn btn-secondary" OnClick="btnClear_Click" />
                                            <asp:Button ID="btnRefresh" runat="server" Text="更新" CssClass="btn btn-info"
                                                OnClick="btnRefresh_Click" />
                                            <asp:Button ID="btnLowStock" runat="server" Text="在庫不足一覧"
                                                CssClass="btn btn-warning" OnClick="btnLowStock_Click" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- メッセージエリア -->
                            <asp:Panel ID="pnlMessage" runat="server" Visible="false">
                                <div class="alert alert-info">
                                    <asp:Label ID="lblMessage" runat="server"></asp:Label>
                                </div>
                            </asp:Panel>

                            <asp:Panel ID="pnlError" runat="server" Visible="false">
                                <div class="alert alert-danger">
                                    <asp:Label ID="lblError" runat="server"></asp:Label>
                                </div>
                            </asp:Panel>

                            <!-- 在庫一覧テーブル -->
                            <div class="table-responsive">
                                <asp:GridView ID="gvStock" runat="server" CssClass="table table-striped"
                                    AutoGenerateColumns="false" AllowPaging="true" PageSize="20"
                                    OnPageIndexChanging="gvStock_PageIndexChanging" OnRowCommand="gvStock_RowCommand"
                                    OnRowDataBound="gvStock_RowDataBound" EmptyDataText="在庫データがありません。">
                                    <Columns>
                                        <asp:BoundField DataField="PRODUCT_CD" HeaderText="商品コード" />
                                        <asp:BoundField DataField="PRODUCT_NAME" HeaderText="商品名" />
                                        <asp:BoundField DataField="CATEGORY_NAME" HeaderText="カテゴリ" />
                                        <asp:BoundField DataField="CURRENT_STOCK" HeaderText="現在庫数"
                                            DataFormatString="{0:N0}" ItemStyle-HorizontalAlign="Right" />
                                        <asp:BoundField DataField="RESERVED_STOCK" HeaderText="引当済"
                                            DataFormatString="{0:N0}" ItemStyle-HorizontalAlign="Right" />
                                        <asp:BoundField DataField="AVAILABLE_STOCK" HeaderText="利用可能"
                                            DataFormatString="{0:N0}" ItemStyle-HorizontalAlign="Right" />
                                        <asp:BoundField DataField="MIN_STOCK" HeaderText="最小在庫数"
                                            DataFormatString="{0:N0}" ItemStyle-HorizontalAlign="Right" />
                                        <asp:BoundField DataField="UNIT" HeaderText="単位" />
                                        <asp:TemplateField HeaderText="状態" ItemStyle-Width="80px"
                                            ItemStyle-HorizontalAlign="Center">
                                            <ItemTemplate>
                                                <asp:Label ID="lblStockStatus" runat="server"></asp:Label>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        <asp:BoundField DataField="LAST_UPDATE" HeaderText="最終更新"
                                            DataFormatString="{0:MM/dd HH:mm}" />
                                        <asp:TemplateField HeaderText="操作" ItemStyle-Width="100px"
                                            ItemStyle-HorizontalAlign="Center">
                                            <ItemTemplate>
                                                <asp:Button ID="btnAdjust" runat="server" Text="調整"
                                                    CssClass="btn btn-sm btn-warning" CommandName="AdjustStock"
                                                    CommandArgument='<%# Eval("PRODUCT_CD") %>' />
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                    </Columns>
                                    <PagerStyle CssClass="pagination-wrapper" />
                                </asp:GridView>
                            </div>

                            <!-- 件数表示 -->
                            <div class="mt-3">
                                <asp:Label ID="lblRecordCount" runat="server" CssClass="text-muted"></asp:Label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="footer">
                    <p>&copy; 2024 在庫管理システム - Powered by Asphalt Framework</p>
                </div>
            </form>
        </body>

        </html>

        <style>
            .row {
                display: flex;
                flex-wrap: wrap;
                margin: -0.5rem;
            }

            .col-md-2 {
                flex: 0 0 16.666667%;
                max-width: 16.666667%;
                padding: 0.5rem;
            }

            .col-md-3 {
                flex: 0 0 25%;
                max-width: 25%;
                padding: 0.5rem;
            }

            .col-md-4 {
                flex: 0 0 33.333333%;
                max-width: 33.333333%;
                padding: 0.5rem;
            }

            .mb-4 {
                margin-bottom: 2rem;
            }

            .stat-card {
                background: white;
                border-radius: 8px;
                padding: 1.5rem;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                display: flex;
                align-items: center;
                border-left: 4px solid #3498db;
            }

            .stat-card.alert-warning {
                border-left-color: #f39c12;
            }

            .stat-card.alert-danger {
                border-left-color: #e74c3c;
            }

            .stat-card.alert-info {
                border-left-color: #17a2b8;
            }

            .stat-icon {
                font-size: 2rem;
                margin-right: 1rem;
            }

            .stat-content {
                flex: 1;
            }

            .stat-number {
                font-size: 2rem;
                font-weight: bold;
                color: #2c3e50;
            }

            .stat-label {
                color: #7f8c8d;
                font-size: 0.9rem;
            }

            .search-area {
                background-color: #f8f9fa;
                padding: 1rem;
                border-radius: 4px;
                margin-bottom: 1rem;
            }

            .form-check {
                margin-top: 1.5rem;
            }

            .table-responsive {
                overflow-x: auto;
            }

            .btn-sm {
                padding: 0.25rem 0.5rem;
                font-size: 0.875rem;
                margin: 0 2px;
            }

            .pagination-wrapper {
                text-align: center;
                margin-top: 1rem;
            }

            .menu .active {
                background-color: #2c3e50;
                font-weight: bold;
            }

            .stock-status-normal {
                color: #27ae60;
                font-weight: bold;
            }

            .stock-status-low {
                color: #f39c12;
                font-weight: bold;
            }

            .stock-status-out {
                color: #e74c3c;
                font-weight: bold;
            }

            @media (max-width: 768px) {

                .col-md-2,
                .col-md-3,
                .col-md-4 {
                    flex: 0 0 100%;
                    max-width: 100%;
                }

                .stat-card {
                    margin-bottom: 1rem;
                }
            }
        </style>