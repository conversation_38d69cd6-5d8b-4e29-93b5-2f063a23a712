Imports System.Data
Imports Whouse.Application.Logic.Stock

Partial Class StockList
    Inherits System.Web.UI.Page

    Private stockLogic As StockLogic

    Protected Sub Page_Load(sender As Object, e As EventArgs) Handles Me.Load
        ' ログインチェック
        If Session("LOGIN_ID") Is Nothing OrElse Session("LOGIN_ID") = "" Then
            Response.Redirect("../Login.aspx")
            Return
        End If

        ' ユーザー情報表示
        lblUserInfo.Text = "ユーザー: " & Session("LOGIN_ID").ToString() & " | "

        ' ロジッククラス初期化
        Dim security As New Dbs.Asphalt.Core.SystemLogic.Security("")
        security.UserID = Session("LOGIN_ID").ToString()
        stockLogic = New StockLogic(security)

        If Not IsPostBack Then
            ' カテゴリドロップダウンの初期化
            LoadCategoryDropDown()
            
            ' 在庫統計の読み込み
            LoadStockStatistics()
            
            ' 在庫一覧の初期表示
            LoadStockList()
        End If
    End Sub

    ''' <summary>
    ''' カテゴリドロップダウンを読み込み
    ''' </summary>
    Private Sub LoadCategoryDropDown()
        Try
            Dim categoryDt As DataTable = stockLogic.GetCategoryDropDownList()
            If categoryDt IsNot Nothing Then
                ddlCategory.DataSource = categoryDt
                ddlCategory.DataTextField = "CATEGORY_NAME"
                ddlCategory.DataValueField = "CATEGORY_CD"
                ddlCategory.DataBind()
                
                ' 先頭に「全て」を追加
                ddlCategory.Items.Insert(0, New ListItem("-- 全て --", ""))
            End If
        Catch ex As Exception
            ShowError("カテゴリ読み込みでエラーが発生しました: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' 在庫統計を読み込み
    ''' </summary>
    Private Sub LoadStockStatistics()
        Try
            Dim statisticsDt As DataTable = stockLogic.GetStockStatistics()
            If statisticsDt IsNot Nothing AndAlso statisticsDt.Rows.Count > 0 Then
                Dim row As DataRow = statisticsDt.Rows(0)
                lblTotalProducts.Text = row("TOTAL_PRODUCTS").ToString()
                lblLowStockProducts.Text = row("LOW_STOCK_PRODUCTS").ToString()
                lblOutOfStockProducts.Text = row("OUT_OF_STOCK_PRODUCTS").ToString()
                lblTotalStockQty.Text = CInt(row("TOTAL_STOCK_QTY")).ToString("N0")
            End If
        Catch ex As Exception
            ShowError("在庫統計読み込みでエラーが発生しました: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' 在庫一覧を読み込み
    ''' </summary>
    Private Sub LoadStockList()
        Try
            Dim stockDt As DataTable

            ' 検索条件がある場合は検索、ない場合は全件取得
            If Not String.IsNullOrEmpty(txtKeyword.Text) OrElse Not String.IsNullOrEmpty(ddlCategory.SelectedValue) OrElse chkLowStockOnly.Checked Then
                stockDt = stockLogic.SearchStock(txtKeyword.Text.Trim(), ddlCategory.SelectedValue, chkLowStockOnly.Checked)
            Else
                stockDt = stockLogic.GetStockList()
            End If

            If stockDt IsNot Nothing Then
                gvStock.DataSource = stockDt
                gvStock.DataBind()
                
                ' 件数表示
                lblRecordCount.Text = $"検索結果: {stockDt.Rows.Count} 件"
            Else
                ShowError(stockLogic.LastError)
            End If

        Catch ex As Exception
            ShowError("在庫一覧読み込みでエラーが発生しました: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' 検索ボタンクリック
    ''' </summary>
    Protected Sub btnSearch_Click(sender As Object, e As EventArgs)
        gvStock.PageIndex = 0 ' ページを先頭に戻す
        LoadStockList()
    End Sub

    ''' <summary>
    ''' クリアボタンクリック
    ''' </summary>
    Protected Sub btnClear_Click(sender As Object, e As EventArgs)
        txtKeyword.Text = ""
        ddlCategory.SelectedIndex = 0
        chkLowStockOnly.Checked = False
        gvStock.PageIndex = 0
        LoadStockList()
    End Sub

    ''' <summary>
    ''' 更新ボタンクリック
    ''' </summary>
    Protected Sub btnRefresh_Click(sender As Object, e As EventArgs)
        LoadStockStatistics()
        LoadStockList()
        ShowMessage("在庫情報を更新しました。")
    End Sub

    ''' <summary>
    ''' 在庫不足一覧ボタンクリック
    ''' </summary>
    Protected Sub btnLowStock_Click(sender As Object, e As EventArgs)
        Response.Redirect("LowStockAlert.aspx")
    End Sub

    ''' <summary>
    ''' GridViewページング
    ''' </summary>
    Protected Sub gvStock_PageIndexChanging(sender As Object, e As GridViewPageEventArgs)
        gvStock.PageIndex = e.NewPageIndex
        LoadStockList()
    End Sub

    ''' <summary>
    ''' GridView行コマンド
    ''' </summary>
    Protected Sub gvStock_RowCommand(sender As Object, e As GridViewCommandEventArgs)
        Dim productCode As String = e.CommandArgument.ToString()

        Select Case e.CommandName
            Case "AdjustStock"
                ' 在庫調整画面に遷移
                Response.Redirect($"StockAdjust.aspx?code={Server.UrlEncode(productCode)}")
        End Select
    End Sub

    ''' <summary>
    ''' GridView行データバインド
    ''' </summary>
    Protected Sub gvStock_RowDataBound(sender As Object, e As GridViewRowEventArgs)
        If e.Row.RowType = DataControlRowType.DataRow Then
            Dim rowView As DataRowView = CType(e.Row.DataItem, DataRowView)
            Dim availableStock As Integer = CInt(rowView("AVAILABLE_STOCK"))
            Dim minStock As Integer = CInt(rowView("MIN_STOCK"))
            Dim currentStock As Integer = CInt(rowView("CURRENT_STOCK"))

            ' 在庫状態ラベルを設定
            Dim lblStockStatus As Label = CType(e.Row.FindControl("lblStockStatus"), Label)
            If lblStockStatus IsNot Nothing Then
                If currentStock = 0 Then
                    lblStockStatus.Text = "在庫切れ"
                    lblStockStatus.CssClass = "stock-status-out"
                ElseIf availableStock <= minStock Then
                    lblStockStatus.Text = "在庫不足"
                    lblStockStatus.CssClass = "stock-status-low"
                Else
                    lblStockStatus.Text = "正常"
                    lblStockStatus.CssClass = "stock-status-normal"
                End If
            End If

            ' 在庫不足の行は背景色を変更
            If availableStock <= minStock Then
                e.Row.CssClass = "table-warning"
            End If
            
            ' 在庫切れの行は背景色を変更
            If currentStock = 0 Then
                e.Row.CssClass = "table-danger"
            End If
        End If
    End Sub

    ''' <summary>
    ''' ログアウトボタンクリック
    ''' </summary>
    Protected Sub lnkLogout_Click(sender As Object, e As EventArgs)
        Session.Clear()
        Session.Abandon()
        Response.Redirect("../Login.aspx")
    End Sub

    ''' <summary>
    ''' 成功メッセージ表示
    ''' </summary>
    ''' <param name="message">メッセージ</param>
    Private Sub ShowMessage(message As String)
        lblMessage.Text = message
        pnlMessage.Visible = True
        pnlError.Visible = False
    End Sub

    ''' <summary>
    ''' エラーメッセージ表示
    ''' </summary>
    ''' <param name="message">エラーメッセージ</param>
    Private Sub ShowError(message As String)
        lblError.Text = message
        pnlError.Visible = True
        pnlMessage.Visible = False
    End Sub

End Class
