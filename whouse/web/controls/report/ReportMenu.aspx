<%@ Page Language="VB" AutoEventWireup="false" CodeFile="ReportMenu.aspx.vb" Inherits="ReportMenu" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>レポート - 在庫管理システム</title>
    <link href="../../css/default.css" rel="stylesheet" type="text/css" />
</head>
<body>
    <form id="form1" runat="server">
        <div class="header">
            <h1>在庫管理システム</h1>
            <div class="user-info">
                <asp:Label ID="lblUserInfo" runat="server"></asp:Label>
                <asp:LinkButton ID="lnkLogout" runat="server" Text="ログアウト" OnClick="lnkLogout_Click"></asp:LinkButton>
            </div>
        </div>
        
        <div class="navigation">
            <ul class="menu">
                <li><a href="../product/ProductList.aspx">商品管理</a></li>
                <li><a href="../stock/StockList.aspx">在庫管理</a></li>
                <li><a href="../transaction/TransactionMenu.aspx">入出庫管理</a></li>
                <li><a href="../report/ReportMenu.aspx" class="active">レポート</a></li>
                <li><a href="../system/SystemMenu.aspx">システム管理</a></li>
            </ul>
        </div>
        
        <div class="content">
            <div class="row">
                <!-- 在庫関連レポート -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3>📊 在庫関連レポート</h3>
                        </div>
                        <div class="card-body">
                            <div class="menu-grid">
                                <div class="menu-item">
                                    <asp:Button ID="btnStockReport" runat="server" Text="在庫一覧レポート" CssClass="btn btn-primary btn-lg btn-block" OnClick="btnStockReport_Click" />
                                    <p class="menu-description">現在の在庫状況を一覧で出力します</p>
                                </div>
                                <div class="menu-item">
                                    <asp:Button ID="btnLowStockReport" runat="server" Text="在庫不足レポート" CssClass="btn btn-warning btn-lg btn-block" OnClick="btnLowStockReport_Click" />
                                    <p class="menu-description">在庫不足商品の一覧を出力します</p>
                                </div>
                                <div class="menu-item">
                                    <asp:Button ID="btnStockMovement" runat="server" Text="在庫推移レポート" CssClass="btn btn-info btn-lg btn-block" OnClick="btnStockMovement_Click" />
                                    <p class="menu-description">商品別の在庫推移を確認します</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 入出庫関連レポート -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3>📈 入出庫関連レポート</h3>
                        </div>
                        <div class="card-body">
                            <div class="menu-grid">
                                <div class="menu-item">
                                    <asp:Button ID="btnReceiptReport" runat="server" Text="入庫履歴レポート" CssClass="btn btn-success btn-lg btn-block" OnClick="btnReceiptReport_Click" />
                                    <p class="menu-description">期間指定で入庫履歴を出力します</p>
                                </div>
                                <div class="menu-item">
                                    <asp:Button ID="btnIssueReport" runat="server" Text="出庫履歴レポート" CssClass="btn btn-danger btn-lg btn-block" OnClick="btnIssueReport_Click" />
                                    <p class="menu-description">期間指定で出庫履歴を出力します</p>
                                </div>
                                <div class="menu-item">
                                    <asp:Button ID="btnTransactionReport" runat="server" Text="入出庫統計レポート" CssClass="btn btn-secondary btn-lg btn-block" OnClick="btnTransactionReport_Click" />
                                    <p class="menu-description">入出庫の統計情報を出力します</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 分析レポート -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3>🔍 分析レポート</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="menu-item">
                                        <asp:Button ID="btnProductSalesReport" runat="server" Text="商品別売上レポート" CssClass="btn btn-info btn-lg btn-block" OnClick="btnProductSalesReport_Click" />
                                        <p class="menu-description">商品別の出庫実績を確認します</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="menu-item">
                                        <asp:Button ID="btnMonthlySummary" runat="server" Text="月次サマリーレポート" CssClass="btn btn-success btn-lg btn-block" OnClick="btnMonthlySummary_Click" />
                                        <p class="menu-description">月次の在庫・入出庫サマリーを出力します</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="menu-item">
                                        <asp:Button ID="btnABCAnalysis" runat="server" Text="ABC分析レポート" CssClass="btn btn-warning btn-lg btn-block" OnClick="btnABCAnalysis_Click" />
                                        <p class="menu-description">商品のABC分析結果を出力します</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="menu-item">
                                        <asp:Button ID="btnBackToMain" runat="server" Text="メインメニュー" CssClass="btn btn-outline-primary btn-lg btn-block" OnClick="btnBackToMain_Click" />
                                        <p class="menu-description">メインメニューに戻ります</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- クイックレポート -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3>⚡ クイックレポート</h3>
                        </div>
                        <div class="card-body">
                            <div class="quick-report-area">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="quick-report-item">
                                            <h5>本日の入出庫状況</h5>
                                            <div class="quick-stats">
                                                <div class="stat-item">
                                                    <span class="stat-label">入庫件数:</span>
                                                    <span class="stat-value"><asp:Label ID="lblTodayReceipts" runat="server" Text="0"></asp:Label>件</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">出庫件数:</span>
                                                    <span class="stat-value"><asp:Label ID="lblTodayIssues" runat="server" Text="0"></asp:Label>件</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="quick-report-item">
                                            <h5>在庫アラート</h5>
                                            <div class="quick-stats">
                                                <div class="stat-item">
                                                    <span class="stat-label">在庫不足:</span>
                                                    <span class="stat-value alert-warning"><asp:Label ID="lblLowStockCount" runat="server" Text="0"></asp:Label>商品</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">在庫切れ:</span>
                                                    <span class="stat-value alert-danger"><asp:Label ID="lblOutOfStockCount" runat="server" Text="0"></asp:Label>商品</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="quick-report-item">
                                            <h5>今月の実績</h5>
                                            <div class="quick-stats">
                                                <div class="stat-item">
                                                    <span class="stat-label">入庫総数:</span>
                                                    <span class="stat-value"><asp:Label ID="lblMonthReceiptQty" runat="server" Text="0"></asp:Label></span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">出庫総数:</span>
                                                    <span class="stat-value"><asp:Label ID="lblMonthIssueQty" runat="server" Text="0"></asp:Label></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; 2024 在庫管理システム - Powered by Asphalt Framework</p>
        </div>
    </form>
</body>
</html>

<style>
.row {
    display: flex;
    flex-wrap: wrap;
    margin: -0.5rem;
}

.col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
    padding: 0.5rem;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding: 0.5rem;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0.5rem;
}

.col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0.5rem;
}

.mt-4 {
    margin-top: 2rem;
}

.menu-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.menu-item {
    text-align: center;
}

.menu-item .btn {
    margin-bottom: 0.5rem;
}

.menu-description {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

.btn-block {
    display: block;
    width: 100%;
}

.card h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.3rem;
}

.menu .active {
    background-color: #2c3e50;
    font-weight: bold;
}

.quick-report-area {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 1rem;
}

.quick-report-item {
    background-color: white;
    border-radius: 4px;
    padding: 1rem;
    height: 100%;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.quick-report-item h5 {
    margin-bottom: 1rem;
    color: #2c3e50;
    font-size: 1rem;
}

.quick-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
}

.stat-value {
    font-weight: bold;
    color: #2c3e50;
}

.stat-value.alert-warning {
    color: #f39c12;
}

.stat-value.alert-danger {
    color: #e74c3c;
}

@media (max-width: 768px) {
    .col-md-3, .col-md-4, .col-md-6, .col-md-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .row {
        margin: 0;
    }
    
    .col-md-3, .col-md-4, .col-md-6, .col-md-12 {
        padding: 0.25rem;
    }
    
    .quick-report-item {
        margin-bottom: 1rem;
    }
}
</style>
