<%@ Page Language="VB" AutoEventWireup="false" CodeFile="TransactionMenu.aspx.vb" Inherits="TransactionMenu" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>入出庫管理 - 在庫管理システム</title>
    <link href="../../css/default.css" rel="stylesheet" type="text/css" />
</head>
<body>
    <form id="form1" runat="server">
        <div class="header">
            <h1>在庫管理システム</h1>
            <div class="user-info">
                <asp:Label ID="lblUserInfo" runat="server"></asp:Label>
                <asp:LinkButton ID="lnkLogout" runat="server" Text="ログアウト" OnClick="lnkLogout_Click"></asp:LinkButton>
            </div>
        </div>
        
        <div class="navigation">
            <ul class="menu">
                <li><a href="../product/ProductList.aspx">商品管理</a></li>
                <li><a href="../stock/StockList.aspx">在庫管理</a></li>
                <li><a href="../transaction/TransactionMenu.aspx" class="active">入出庫管理</a></li>
                <li><a href="../report/ReportMenu.aspx">レポート</a></li>
                <li><a href="../system/SystemMenu.aspx">システム管理</a></li>
            </ul>
        </div>
        
        <div class="content">
            <!-- 入出庫統計カード -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">📥</div>
                        <div class="stat-content">
                            <div class="stat-number"><asp:Label ID="lblTodayReceipts" runat="server" Text="0"></asp:Label></div>
                            <div class="stat-label">本日入庫件数</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">📤</div>
                        <div class="stat-content">
                            <div class="stat-number"><asp:Label ID="lblTodayIssues" runat="server" Text="0"></asp:Label></div>
                            <div class="stat-label">本日出庫件数</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card alert-info">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <div class="stat-number"><asp:Label ID="lblMonthReceipts" runat="server" Text="0"></asp:Label></div>
                            <div class="stat-label">今月入庫件数</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card alert-info">
                        <div class="stat-icon">📈</div>
                        <div class="stat-content">
                            <div class="stat-number"><asp:Label ID="lblMonthIssues" runat="server" Text="0"></asp:Label></div>
                            <div class="stat-label">今月出庫件数</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- 入庫管理メニュー -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3>📥 入庫管理</h3>
                        </div>
                        <div class="card-body">
                            <div class="menu-grid">
                                <div class="menu-item">
                                    <asp:Button ID="btnReceiptEntry" runat="server" Text="入庫登録" CssClass="btn btn-primary btn-lg btn-block" OnClick="btnReceiptEntry_Click" />
                                    <p class="menu-description">商品の入庫を登録します</p>
                                </div>
                                <div class="menu-item">
                                    <asp:Button ID="btnReceiptHistory" runat="server" Text="入庫履歴" CssClass="btn btn-info btn-lg btn-block" OnClick="btnReceiptHistory_Click" />
                                    <p class="menu-description">入庫履歴を確認・検索します</p>
                                </div>
                                <div class="menu-item">
                                    <asp:Button ID="btnReceiptReport" runat="server" Text="入庫レポート" CssClass="btn btn-secondary btn-lg btn-block" OnClick="btnReceiptReport_Click" />
                                    <p class="menu-description">入庫実績レポートを出力します</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 出庫管理メニュー -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3>📤 出庫管理</h3>
                        </div>
                        <div class="card-body">
                            <div class="menu-grid">
                                <div class="menu-item">
                                    <asp:Button ID="btnIssueEntry" runat="server" Text="出庫登録" CssClass="btn btn-warning btn-lg btn-block" OnClick="btnIssueEntry_Click" />
                                    <p class="menu-description">商品の出庫を登録します</p>
                                </div>
                                <div class="menu-item">
                                    <asp:Button ID="btnIssueHistory" runat="server" Text="出庫履歴" CssClass="btn btn-info btn-lg btn-block" OnClick="btnIssueHistory_Click" />
                                    <p class="menu-description">出庫履歴を確認・検索します</p>
                                </div>
                                <div class="menu-item">
                                    <asp:Button ID="btnIssueReport" runat="server" Text="出庫レポート" CssClass="btn btn-secondary btn-lg btn-block" OnClick="btnIssueReport_Click" />
                                    <p class="menu-description">出庫実績レポートを出力します</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 統合管理メニュー -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3>📋 統合管理</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="menu-item">
                                        <asp:Button ID="btnTransactionHistory" runat="server" Text="入出庫履歴" CssClass="btn btn-success btn-lg btn-block" OnClick="btnTransactionHistory_Click" />
                                        <p class="menu-description">入出庫の統合履歴を確認します</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="menu-item">
                                        <asp:Button ID="btnTransactionReport" runat="server" Text="入出庫レポート" CssClass="btn btn-success btn-lg btn-block" OnClick="btnTransactionReport_Click" />
                                        <p class="menu-description">入出庫統計レポートを出力します</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="menu-item">
                                        <asp:Button ID="btnStockMovement" runat="server" Text="在庫推移" CssClass="btn btn-info btn-lg btn-block" OnClick="btnStockMovement_Click" />
                                        <p class="menu-description">在庫の推移を確認します</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="menu-item">
                                        <asp:Button ID="btnBackToMain" runat="server" Text="メインメニュー" CssClass="btn btn-outline-primary btn-lg btn-block" OnClick="btnBackToMain_Click" />
                                        <p class="menu-description">メインメニューに戻ります</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; 2024 在庫管理システム - Powered by Asphalt Framework</p>
        </div>
    </form>
</body>
</html>

<style>
.row {
    display: flex;
    flex-wrap: wrap;
    margin: -0.5rem;
}

.col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
    padding: 0.5rem;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0.5rem;
}

.col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0.5rem;
}

.mb-4 {
    margin-bottom: 2rem;
}

.mt-4 {
    margin-top: 2rem;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    border-left: 4px solid #3498db;
    height: 100%;
}

.stat-card.alert-info {
    border-left-color: #17a2b8;
}

.stat-icon {
    font-size: 2rem;
    margin-right: 1rem;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
}

.stat-label {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.menu-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.menu-item {
    text-align: center;
}

.menu-item .btn {
    margin-bottom: 0.5rem;
}

.menu-description {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

.btn-block {
    display: block;
    width: 100%;
}

.card h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.3rem;
}

.menu .active {
    background-color: #2c3e50;
    font-weight: bold;
}

@media (max-width: 768px) {
    .col-md-3, .col-md-6, .col-md-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .row {
        margin: 0;
    }
    
    .col-md-3, .col-md-6, .col-md-12 {
        padding: 0.25rem;
    }
}
</style>
