Imports System.Data
Imports Whouse.Application.Logic.Transaction

Partial Class TransactionMenu
    Inherits System.Web.UI.Page

    Private transactionLogic As TransactionLogic

    Protected Sub Page_Load(sender As Object, e As EventArgs) Handles Me.Load
        ' ログインチェック
        If Session("LOGIN_ID") Is Nothing OrElse Session("LOGIN_ID") = "" Then
            Response.Redirect("../Login.aspx")
            Return
        End If

        ' ユーザー情報表示
        lblUserInfo.Text = "ユーザー: " & Session("LOGIN_ID").ToString() & " | "

        ' ロジッククラス初期化
        Dim security As New Dbs.Asphalt.Core.SystemLogic.Security("")
        security.UserID = Session("LOGIN_ID").ToString()
        transactionLogic = New TransactionLogic(security)

        If Not IsPostBack Then
            ' 統計情報の読み込み
            LoadStatistics()
        End If
    End Sub

    ''' <summary>
    ''' 統計情報を読み込み
    ''' </summary>
    Private Sub LoadStatistics()
        Try
            ' 本日の統計
            Dim todayStats As DataTable = transactionLogic.GetTransactionStatistics(Date.Today, Date.Today)
            If todayStats IsNot Nothing AndAlso todayStats.Rows.Count > 0 Then
                Dim row As DataRow = todayStats.Rows(0)
                lblTodayReceipts.Text = row("RECEIPT_COUNT").ToString()
                lblTodayIssues.Text = row("ISSUE_COUNT").ToString()
            End If

            ' 今月の統計
            Dim monthStart As Date = New Date(Date.Today.Year, Date.Today.Month, 1)
            Dim monthEnd As Date = monthStart.AddMonths(1).AddDays(-1)
            Dim monthStats As DataTable = transactionLogic.GetTransactionStatistics(monthStart, monthEnd)
            If monthStats IsNot Nothing AndAlso monthStats.Rows.Count > 0 Then
                Dim row As DataRow = monthStats.Rows(0)
                lblMonthReceipts.Text = row("RECEIPT_COUNT").ToString()
                lblMonthIssues.Text = row("ISSUE_COUNT").ToString()
            End If

        Catch ex As Exception
            ' 統計情報取得エラーは無視（メニュー表示には影響しない）
        End Try
    End Sub

    ''' <summary>
    ''' 入庫登録ボタンクリック
    ''' </summary>
    Protected Sub btnReceiptEntry_Click(sender As Object, e As EventArgs)
        Response.Redirect("ReceiptEntry.aspx")
    End Sub

    ''' <summary>
    ''' 入庫履歴ボタンクリック
    ''' </summary>
    Protected Sub btnReceiptHistory_Click(sender As Object, e As EventArgs)
        Response.Redirect("ReceiptHistory.aspx")
    End Sub

    ''' <summary>
    ''' 入庫レポートボタンクリック
    ''' </summary>
    Protected Sub btnReceiptReport_Click(sender As Object, e As EventArgs)
        Response.Redirect("../report/ReceiptReport.aspx")
    End Sub

    ''' <summary>
    ''' 出庫登録ボタンクリック
    ''' </summary>
    Protected Sub btnIssueEntry_Click(sender As Object, e As EventArgs)
        Response.Redirect("IssueEntry.aspx")
    End Sub

    ''' <summary>
    ''' 出庫履歴ボタンクリック
    ''' </summary>
    Protected Sub btnIssueHistory_Click(sender As Object, e As EventArgs)
        Response.Redirect("IssueHistory.aspx")
    End Sub

    ''' <summary>
    ''' 出庫レポートボタンクリック
    ''' </summary>
    Protected Sub btnIssueReport_Click(sender As Object, e As EventArgs)
        Response.Redirect("../report/IssueReport.aspx")
    End Sub

    ''' <summary>
    ''' 入出庫履歴ボタンクリック
    ''' </summary>
    Protected Sub btnTransactionHistory_Click(sender As Object, e As EventArgs)
        Response.Redirect("TransactionHistory.aspx")
    End Sub

    ''' <summary>
    ''' 入出庫レポートボタンクリック
    ''' </summary>
    Protected Sub btnTransactionReport_Click(sender As Object, e As EventArgs)
        Response.Redirect("../report/TransactionReport.aspx")
    End Sub

    ''' <summary>
    ''' 在庫推移ボタンクリック
    ''' </summary>
    Protected Sub btnStockMovement_Click(sender As Object, e As EventArgs)
        Response.Redirect("../report/StockMovement.aspx")
    End Sub

    ''' <summary>
    ''' メインメニューボタンクリック
    ''' </summary>
    Protected Sub btnBackToMain_Click(sender As Object, e As EventArgs)
        Response.Redirect("../../default.aspx")
    End Sub

    ''' <summary>
    ''' ログアウトボタンクリック
    ''' </summary>
    Protected Sub lnkLogout_Click(sender As Object, e As EventArgs)
        Session.Clear()
        Session.Abandon()
        Response.Redirect("../Login.aspx")
    End Sub

End Class
