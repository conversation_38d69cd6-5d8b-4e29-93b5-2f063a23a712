Imports System.Data
Imports Whouse.Application.Logic.Transaction
Imports Whouse.Application.Logic.Product
Imports Whouse.Application.Logic.Stock

Partial Class ReceiptEntry
    Inherits System.Web.UI.Page

    Private transactionLogic As TransactionLogic
    Private productLogic As ProductLogic
    Private stockLogic As StockLogic

    Protected Sub Page_Load(sender As Object, e As EventArgs) Handles Me.Load
        ' ログインチェック
        If Session("LOGIN_ID") Is Nothing OrElse Session("LOGIN_ID") = "" Then
            Response.Redirect("../Login.aspx")
            Return
        End If

        ' ユーザー情報表示
        lblUserInfo.Text = "ユーザー: " & Session("LOGIN_ID").ToString() & " | "

        ' ロジッククラス初期化
        Dim security As New Dbs.Asphalt.Core.SystemLogic.Security("")
        security.UserID = Session("LOGIN_ID").ToString()
        transactionLogic = New TransactionLogic(security)
        productLogic = New ProductLogic(security)
        stockLogic = New StockLogic(security)

        If Not IsPostBack Then
            ' 初期値設定
            txtReceiptDate.Text = Date.Today.ToString("yyyy-MM-dd")
        End If
    End Sub

    ''' <summary>
    ''' 商品検索ボタンクリック
    ''' </summary>
    Protected Sub btnSearchProduct_Click(sender As Object, e As EventArgs)
        Try
            Dim productCode As String = txtProductCode.Text.Trim()
            If String.IsNullOrEmpty(productCode) Then
                ShowError("商品コードを入力してください。")
                txtProductCode.Focus()
                Return
            End If

            ' 商品情報を取得
            Dim productRow As DataRow = productLogic.GetProduct(productCode)
            If productRow IsNot Nothing Then
                ' 商品情報を表示
                lblProductName.Text = productRow("PRODUCT_NAME").ToString()
                lblUnit.Text = productRow("UNIT").ToString()
                
                ' 在庫情報を取得・表示
                LoadStockInfo(productCode)
                
                ' 数量フィールドにフォーカス
                txtQuantity.Focus()
            Else
                ShowError("指定された商品コードが見つかりません。")
                ClearProductInfo()
                txtProductCode.Focus()
            End If

        Catch ex As Exception
            ShowError("商品検索でエラーが発生しました: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' 入庫登録ボタンクリック
    ''' </summary>
    Protected Sub btnRegister_Click(sender As Object, e As EventArgs)
        Try
            ' 入力値取得
            Dim receiptDate As Date
            Dim productCode As String = txtProductCode.Text.Trim()
            Dim quantity As Integer
            Dim unitCost As Decimal?
            Dim supplier As String = txtSupplier.Text.Trim()
            Dim remarks As String = txtRemarks.Text.Trim()

            ' 入力値検証
            If Not Date.TryParse(txtReceiptDate.Text, receiptDate) Then
                ShowError("入庫日を正しい形式で入力してください。")
                txtReceiptDate.Focus()
                Return
            End If

            If String.IsNullOrEmpty(productCode) Then
                ShowError("商品コードを入力してください。")
                txtProductCode.Focus()
                Return
            End If

            If Not Integer.TryParse(txtQuantity.Text, quantity) OrElse quantity <= 0 Then
                ShowError("入庫数量は1以上の整数で入力してください。")
                txtQuantity.Focus()
                Return
            End If

            ' 仕入単価の変換（省略可）
            If Not String.IsNullOrEmpty(txtUnitCost.Text) Then
                Dim tempUnitCost As Decimal
                If Decimal.TryParse(txtUnitCost.Text, tempUnitCost) Then
                    unitCost = tempUnitCost
                Else
                    ShowError("仕入単価は数値で入力してください。")
                    txtUnitCost.Focus()
                    Return
                End If
            End If

            ' 商品存在チェック
            If productLogic.GetProduct(productCode) Is Nothing Then
                ShowError("指定された商品が存在しません。商品検索ボタンで確認してください。")
                txtProductCode.Focus()
                Return
            End If

            ' 入庫処理実行
            If transactionLogic.ProcessReceipt(receiptDate, productCode, quantity, unitCost, supplier, remarks) Then
                ShowMessage(transactionLogic.LastMessage)
                ClearForm()
                txtReceiptDate.Text = Date.Today.ToString("yyyy-MM-dd")
                txtProductCode.Focus()
            Else
                ShowError(transactionLogic.LastError)
            End If

        Catch ex As Exception
            ShowError("入庫登録でエラーが発生しました: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' クリアボタンクリック
    ''' </summary>
    Protected Sub btnClear_Click(sender As Object, e As EventArgs)
        ClearForm()
        txtReceiptDate.Text = Date.Today.ToString("yyyy-MM-dd")
        txtProductCode.Focus()
        HideMessages()
    End Sub

    ''' <summary>
    ''' 入庫履歴ボタンクリック
    ''' </summary>
    Protected Sub btnHistory_Click(sender As Object, e As EventArgs)
        Response.Redirect("ReceiptHistory.aspx")
    End Sub

    ''' <summary>
    ''' メニューに戻るボタンクリック
    ''' </summary>
    Protected Sub btnBack_Click(sender As Object, e As EventArgs)
        Response.Redirect("TransactionMenu.aspx")
    End Sub

    ''' <summary>
    ''' ログアウトボタンクリック
    ''' </summary>
    Protected Sub lnkLogout_Click(sender As Object, e As EventArgs)
        Session.Clear()
        Session.Abandon()
        Response.Redirect("../Login.aspx")
    End Sub

    ''' <summary>
    ''' 在庫情報を読み込み
    ''' </summary>
    ''' <param name="productCode">商品コード</param>
    Private Sub LoadStockInfo(productCode As String)
        Try
            Dim stockRow As DataRow = stockLogic.GetStockList().AsEnumerable().FirstOrDefault(Function(r) r("PRODUCT_CD").ToString() = productCode)
            
            If stockRow IsNot Nothing Then
                lblCurrentStock.Text = CInt(stockRow("CURRENT_STOCK")).ToString("N0")
                lblReservedStock.Text = CInt(stockRow("RESERVED_STOCK")).ToString("N0")
                lblAvailableStock.Text = CInt(stockRow("AVAILABLE_STOCK")).ToString("N0")
                lblMinStock.Text = CInt(stockRow("MIN_STOCK")).ToString("N0")
                
                ' 在庫情報パネルを表示
                stockInfoPanel.Visible = True
            Else
                ' 在庫データが存在しない場合は0で表示
                lblCurrentStock.Text = "0"
                lblReservedStock.Text = "0"
                lblAvailableStock.Text = "0"
                lblMinStock.Text = "0"
                stockInfoPanel.Visible = True
            End If

        Catch ex As Exception
            ' 在庫情報取得エラーは無視（入庫処理には影響しない）
            stockInfoPanel.Visible = False
        End Try
    End Sub

    ''' <summary>
    ''' 商品情報をクリア
    ''' </summary>
    Private Sub ClearProductInfo()
        lblProductName.Text = "商品を選択してください"
        lblUnit.Text = "-"
        stockInfoPanel.Visible = False
    End Sub

    ''' <summary>
    ''' フォームをクリア
    ''' </summary>
    Private Sub ClearForm()
        txtProductCode.Text = ""
        txtQuantity.Text = "1"
        txtUnitCost.Text = ""
        txtSupplier.Text = ""
        txtRemarks.Text = ""
        ClearProductInfo()
    End Sub

    ''' <summary>
    ''' 成功メッセージ表示
    ''' </summary>
    ''' <param name="message">メッセージ</param>
    Private Sub ShowMessage(message As String)
        lblMessage.Text = message
        pnlMessage.Visible = True
        pnlError.Visible = False
    End Sub

    ''' <summary>
    ''' エラーメッセージ表示
    ''' </summary>
    ''' <param name="message">エラーメッセージ</param>
    Private Sub ShowError(message As String)
        lblError.Text = message
        pnlError.Visible = True
        pnlMessage.Visible = False
    End Sub

    ''' <summary>
    ''' メッセージ非表示
    ''' </summary>
    Private Sub HideMessages()
        pnlMessage.Visible = False
        pnlError.Visible = False
    End Sub

End Class
