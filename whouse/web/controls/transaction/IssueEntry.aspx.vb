Imports System.Data
Imports Whouse.Application.Logic.Transaction
Imports Whouse.Application.Logic.Product
Imports Whouse.Application.Logic.Stock

Partial Class IssueEntry
    Inherits System.Web.UI.Page

    Private transactionLogic As TransactionLogic
    Private productLogic As ProductLogic
    Private stockLogic As StockLogic

    Protected Sub Page_Load(sender As Object, e As EventArgs) Handles Me.Load
        ' ログインチェック
        If Session("LOGIN_ID") Is Nothing OrElse Session("LOGIN_ID") = "" Then
            Response.Redirect("../Login.aspx")
            Return
        End If

        ' ユーザー情報表示
        lblUserInfo.Text = "ユーザー: " & Session("LOGIN_ID").ToString() & " | "

        ' ロジッククラス初期化
        Dim security As New Dbs.Asphalt.Core.SystemLogic.Security("")
        security.UserID = Session("LOGIN_ID").ToString()
        transactionLogic = New TransactionLogic(security)
        productLogic = New ProductLogic(security)
        stockLogic = New StockLogic(security)

        If Not IsPostBack Then
            ' 初期値設定
            txtIssueDate.Text = Date.Today.ToString("yyyy-MM-dd")
        End If
    End Sub

    ''' <summary>
    ''' 商品検索ボタンクリック
    ''' </summary>
    Protected Sub btnSearchProduct_Click(sender As Object, e As EventArgs)
        Try
            Dim productCode As String = txtProductCode.Text.Trim()
            If String.IsNullOrEmpty(productCode) Then
                ShowError("商品コードを入力してください。")
                txtProductCode.Focus()
                Return
            End If

            ' 商品情報を取得
            Dim productRow As DataRow = productLogic.GetProduct(productCode)
            If productRow IsNot Nothing Then
                ' 商品情報を表示
                lblProductName.Text = productRow("PRODUCT_NAME").ToString()
                lblUnit.Text = productRow("UNIT").ToString()
                
                ' 在庫情報を取得・表示
                LoadStockInfo(productCode)
                
                ' 数量フィールドにフォーカス
                txtQuantity.Focus()
            Else
                ShowError("指定された商品コードが見つかりません。")
                ClearProductInfo()
                txtProductCode.Focus()
            End If

        Catch ex As Exception
            ShowError("商品検索でエラーが発生しました: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' 出庫登録ボタンクリック
    ''' </summary>
    Protected Sub btnRegister_Click(sender As Object, e As EventArgs)
        Try
            ' 入力値取得
            Dim issueDate As Date
            Dim productCode As String = txtProductCode.Text.Trim()
            Dim quantity As Integer
            Dim issueTo As String = txtIssueTo.Text.Trim()
            Dim remarks As String = txtRemarks.Text.Trim()

            ' 入力値検証
            If Not Date.TryParse(txtIssueDate.Text, issueDate) Then
                ShowError("出庫日を正しい形式で入力してください。")
                txtIssueDate.Focus()
                Return
            End If

            If String.IsNullOrEmpty(productCode) Then
                ShowError("商品コードを入力してください。")
                txtProductCode.Focus()
                Return
            End If

            If Not Integer.TryParse(txtQuantity.Text, quantity) OrElse quantity <= 0 Then
                ShowError("出庫数量は1以上の整数で入力してください。")
                txtQuantity.Focus()
                Return
            End If

            ' 商品存在チェック
            If productLogic.GetProduct(productCode) Is Nothing Then
                ShowError("指定された商品が存在しません。商品検索ボタンで確認してください。")
                txtProductCode.Focus()
                Return
            End If

            ' 在庫数チェック
            Dim availableStock As Integer = stockLogic.GetAvailableStock(productCode)
            If availableStock < quantity Then
                ShowError($"在庫不足です。利用可能在庫: {availableStock}, 出庫要求: {quantity}")
                txtQuantity.Focus()
                Return
            End If

            ' 出庫処理実行
            If transactionLogic.ProcessIssue(issueDate, productCode, quantity, issueTo, remarks) Then
                ShowMessage(transactionLogic.LastMessage)
                ClearForm()
                txtIssueDate.Text = Date.Today.ToString("yyyy-MM-dd")
                txtProductCode.Focus()
            Else
                ShowError(transactionLogic.LastError)
            End If

        Catch ex As Exception
            ShowError("出庫登録でエラーが発生しました: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' クリアボタンクリック
    ''' </summary>
    Protected Sub btnClear_Click(sender As Object, e As EventArgs)
        ClearForm()
        txtIssueDate.Text = Date.Today.ToString("yyyy-MM-dd")
        txtProductCode.Focus()
        HideMessages()
    End Sub

    ''' <summary>
    ''' 出庫履歴ボタンクリック
    ''' </summary>
    Protected Sub btnHistory_Click(sender As Object, e As EventArgs)
        Response.Redirect("IssueHistory.aspx")
    End Sub

    ''' <summary>
    ''' メニューに戻るボタンクリック
    ''' </summary>
    Protected Sub btnBack_Click(sender As Object, e As EventArgs)
        Response.Redirect("TransactionMenu.aspx")
    End Sub

    ''' <summary>
    ''' ログアウトボタンクリック
    ''' </summary>
    Protected Sub lnkLogout_Click(sender As Object, e As EventArgs)
        Session.Clear()
        Session.Abandon()
        Response.Redirect("../Login.aspx")
    End Sub

    ''' <summary>
    ''' 在庫情報を読み込み
    ''' </summary>
    ''' <param name="productCode">商品コード</param>
    Private Sub LoadStockInfo(productCode As String)
        Try
            Dim stockRow As DataRow = stockLogic.GetStockList().AsEnumerable().FirstOrDefault(Function(r) r("PRODUCT_CD").ToString() = productCode)
            
            If stockRow IsNot Nothing Then
                Dim currentStock As Integer = CInt(stockRow("CURRENT_STOCK"))
                Dim reservedStock As Integer = CInt(stockRow("RESERVED_STOCK"))
                Dim availableStock As Integer = CInt(stockRow("AVAILABLE_STOCK"))
                Dim minStock As Integer = CInt(stockRow("MIN_STOCK"))

                lblCurrentStock.Text = currentStock.ToString("N0")
                lblReservedStock.Text = reservedStock.ToString("N0")
                lblAvailableStock.Text = availableStock.ToString("N0")
                lblMinStock.Text = minStock.ToString("N0")
                
                ' 在庫警告チェック
                CheckStockWarning(availableStock, minStock, currentStock)
                
                ' 在庫情報パネルを表示
                stockInfoPanel.Visible = True
            Else
                ' 在庫データが存在しない場合は0で表示
                lblCurrentStock.Text = "0"
                lblReservedStock.Text = "0"
                lblAvailableStock.Text = "0"
                lblMinStock.Text = "0"
                
                ' 在庫切れ警告
                stockWarning.Visible = True
                lblStockWarning.Text = "この商品は在庫データが存在しません。"
                stockInfoPanel.Visible = True
            End If

        Catch ex As Exception
            ' 在庫情報取得エラーは無視（出庫処理には影響しない）
            stockInfoPanel.Visible = False
        End Try
    End Sub

    ''' <summary>
    ''' 在庫警告チェック
    ''' </summary>
    ''' <param name="availableStock">利用可能在庫数</param>
    ''' <param name="minStock">最小在庫数</param>
    ''' <param name="currentStock">現在庫数</param>
    Private Sub CheckStockWarning(availableStock As Integer, minStock As Integer, currentStock As Integer)
        stockWarning.Visible = False
        
        If currentStock = 0 Then
            stockWarning.Visible = True
            lblStockWarning.Text = "この商品は在庫切れです。出庫できません。"
        ElseIf availableStock <= minStock Then
            stockWarning.Visible = True
            lblStockWarning.Text = $"この商品は在庫不足です。出庫後の在庫数にご注意ください。（最小在庫数: {minStock}）"
        ElseIf availableStock <= 10 Then
            stockWarning.Visible = True
            lblStockWarning.Text = "この商品の在庫数が少なくなっています。"
        End If
    End Sub

    ''' <summary>
    ''' 商品情報をクリア
    ''' </summary>
    Private Sub ClearProductInfo()
        lblProductName.Text = "商品を選択してください"
        lblUnit.Text = "-"
        stockInfoPanel.Visible = False
        stockWarning.Visible = False
    End Sub

    ''' <summary>
    ''' フォームをクリア
    ''' </summary>
    Private Sub ClearForm()
        txtProductCode.Text = ""
        txtQuantity.Text = "1"
        txtIssueTo.Text = ""
        txtRemarks.Text = ""
        ClearProductInfo()
    End Sub

    ''' <summary>
    ''' 成功メッセージ表示
    ''' </summary>
    ''' <param name="message">メッセージ</param>
    Private Sub ShowMessage(message As String)
        lblMessage.Text = message
        pnlMessage.Visible = True
        pnlError.Visible = False
    End Sub

    ''' <summary>
    ''' エラーメッセージ表示
    ''' </summary>
    ''' <param name="message">エラーメッセージ</param>
    Private Sub ShowError(message As String)
        lblError.Text = message
        pnlError.Visible = True
        pnlMessage.Visible = False
    End Sub

    ''' <summary>
    ''' メッセージ非表示
    ''' </summary>
    Private Sub HideMessages()
        pnlMessage.Visible = False
        pnlError.Visible = False
    End Sub

End Class
