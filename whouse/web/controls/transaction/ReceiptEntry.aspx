<%@ Page Language="VB" AutoEventWireup="false" CodeFile="ReceiptEntry.aspx.vb" Inherits="ReceiptEntry" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>入庫登録 - 在庫管理システム</title>
    <link href="../../css/default.css" rel="stylesheet" type="text/css" />
</head>
<body>
    <form id="form1" runat="server">
        <div class="header">
            <h1>在庫管理システム</h1>
            <div class="user-info">
                <asp:Label ID="lblUserInfo" runat="server"></asp:Label>
                <asp:LinkButton ID="lnkLogout" runat="server" Text="ログアウト" OnClick="lnkLogout_Click"></asp:LinkButton>
            </div>
        </div>
        
        <div class="navigation">
            <ul class="menu">
                <li><a href="../product/ProductList.aspx">商品管理</a></li>
                <li><a href="../stock/StockList.aspx">在庫管理</a></li>
                <li><a href="../transaction/TransactionMenu.aspx" class="active">入出庫管理</a></li>
                <li><a href="../report/ReportMenu.aspx">レポート</a></li>
                <li><a href="../system/SystemMenu.aspx">システム管理</a></li>
            </ul>
        </div>
        
        <div class="content">
            <div class="card">
                <div class="card-header">
                    <h2>入庫登録</h2>
                </div>
                <div class="card-body">
                    <!-- メッセージエリア -->
                    <asp:Panel ID="pnlMessage" runat="server" Visible="false">
                        <div class="alert alert-success">
                            <asp:Label ID="lblMessage" runat="server"></asp:Label>
                        </div>
                    </asp:Panel>
                    
                    <asp:Panel ID="pnlError" runat="server" Visible="false">
                        <div class="alert alert-danger">
                            <asp:Label ID="lblError" runat="server"></asp:Label>
                        </div>
                    </asp:Panel>
                    
                    <!-- 入庫情報入力フォーム -->
                    <div class="form-container">
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label class="form-label required">入庫日:</label>
                                <cc1:TextBox ID="txtReceiptDate" runat="server" CssClass="form-control" TextMode="Date"></cc1:TextBox>
                            </div>
                            <div class="form-group col-md-6">
                                <label class="form-label">仕入先:</label>
                                <cc1:TextBox ID="txtSupplier" runat="server" CssClass="form-control" MaxLength="100" placeholder="仕入先名を入力"></cc1:TextBox>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label class="form-label required">商品コード:</label>
                                <div class="input-group">
                                    <cc1:TextBox ID="txtProductCode" runat="server" CssClass="form-control" MaxLength="20" placeholder="商品コードを入力"></cc1:TextBox>
                                    <div class="input-group-append">
                                        <asp:Button ID="btnSearchProduct" runat="server" Text="検索" CssClass="btn btn-outline-secondary" OnClick="btnSearchProduct_Click" />
                                    </div>
                                </div>
                            </div>
                            <div class="form-group col-md-6">
                                <label class="form-label">商品名:</label>
                                <asp:Label ID="lblProductName" runat="server" CssClass="form-control-plaintext" Text="商品を選択してください"></asp:Label>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-4">
                                <label class="form-label required">入庫数量:</label>
                                <cc1:TextBox ID="txtQuantity" runat="server" CssClass="form-control" Text="1"></cc1:TextBox>
                            </div>
                            <div class="form-group col-md-4">
                                <label class="form-label">単位:</label>
                                <asp:Label ID="lblUnit" runat="server" CssClass="form-control-plaintext" Text="-"></asp:Label>
                            </div>
                            <div class="form-group col-md-4">
                                <label class="form-label">仕入単価:</label>
                                <cc1:TextBox ID="txtUnitCost" runat="server" CssClass="form-control" placeholder="円"></cc1:TextBox>
                                <small class="form-text text-muted">省略可</small>
                            </div>
                        </div>
                        
                        <!-- 現在庫情報表示 -->
                        <div class="stock-info-panel" id="stockInfoPanel" runat="server" visible="false">
                            <h5>現在庫情報</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <label>現在庫数:</label>
                                    <div class="stock-value"><asp:Label ID="lblCurrentStock" runat="server" Text="0"></asp:Label></div>
                                </div>
                                <div class="col-md-3">
                                    <label>引当済:</label>
                                    <div class="stock-value"><asp:Label ID="lblReservedStock" runat="server" Text="0"></asp:Label></div>
                                </div>
                                <div class="col-md-3">
                                    <label>利用可能:</label>
                                    <div class="stock-value"><asp:Label ID="lblAvailableStock" runat="server" Text="0"></asp:Label></div>
                                </div>
                                <div class="col-md-3">
                                    <label>最小在庫数:</label>
                                    <div class="stock-value"><asp:Label ID="lblMinStock" runat="server" Text="0"></asp:Label></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-12">
                                <label class="form-label">備考:</label>
                                <cc1:TextBox ID="txtRemarks" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" MaxLength="500" placeholder="備考があれば入力してください"></cc1:TextBox>
                                <small class="form-text text-muted">最大500文字</small>
                            </div>
                        </div>
                        
                        <!-- ボタンエリア -->
                        <div class="button-area mt-4">
                            <asp:Button ID="btnRegister" runat="server" Text="入庫登録" CssClass="btn btn-primary" OnClick="btnRegister_Click" />
                            <asp:Button ID="btnClear" runat="server" Text="クリア" CssClass="btn btn-secondary" OnClick="btnClear_Click" CausesValidation="false" />
                            <asp:Button ID="btnHistory" runat="server" Text="入庫履歴" CssClass="btn btn-info" OnClick="btnHistory_Click" CausesValidation="false" />
                            <asp:Button ID="btnBack" runat="server" Text="メニューに戻る" CssClass="btn btn-outline-secondary" OnClick="btnBack_Click" CausesValidation="false" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; 2024 在庫管理システム - Powered by Asphalt Framework</p>
        </div>
    </form>
</body>
</html>

<style>
.form-container {
    max-width: 900px;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin: -0.5rem;
}

.form-group {
    padding: 0.5rem;
    margin-bottom: 1rem;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
}

.col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
}

.col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.form-label.required::after {
    content: " *";
    color: #e74c3c;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-control-plaintext {
    display: block;
    width: 100%;
    padding: 0.75rem 0;
    margin-bottom: 0;
    font-size: 14px;
    line-height: 1.5;
    color: #495057;
    background-color: transparent;
    border: solid transparent;
    border-width: 1px 0;
}

.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group .form-control {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    margin-bottom: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group-append {
    margin-left: -1px;
}

.input-group-append .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
    background-color: transparent;
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.stock-info-panel {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
    margin: 1rem 0;
}

.stock-info-panel h5 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.stock-info-panel .row {
    display: flex;
    flex-wrap: wrap;
    margin: -0.25rem;
}

.stock-info-panel .col-md-3 {
    padding: 0.25rem;
}

.stock-info-panel label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.stock-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
}

.form-text {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.text-muted {
    color: #6c757d;
}

.button-area {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #dee2e6;
}

.button-area .btn {
    margin: 0 0.5rem;
}

.mt-4 {
    margin-top: 1.5rem;
}

@media (max-width: 768px) {
    .col-md-3, .col-md-4, .col-md-6, .col-md-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .button-area .btn {
        display: block;
        width: 100%;
        margin: 0.25rem 0;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .input-group .form-control {
        border-radius: 4px;
        margin-bottom: 0.5rem;
    }
    
    .input-group-append {
        margin-left: 0;
    }
    
    .input-group-append .btn {
        border-radius: 4px;
    }
}
</style>
