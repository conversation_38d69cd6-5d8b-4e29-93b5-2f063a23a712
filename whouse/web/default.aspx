<%@ Page Language="VB" Inherits="Dbs.Asphalt.UI.PageBase" %>
<%@ Register Assembly="Dbs.Asphalt.Core" Namespace="Dbs.Asphalt.Core" TagPrefix="cc1" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc2" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>在庫管理システム</title>
    <link href="css/default.css" rel="stylesheet" type="text/css" />
</head>
<body>
    <form id="form1" runat="server">
        <div class="header">
            <h1>在庫管理システム</h1>
            <div class="user-info">
                <asp:Label ID="lblUserInfo" runat="server"></asp:Label>
                <asp:LinkButton ID="lnkLogout" runat="server" Text="ログアウト" OnClick="lnkLogout_Click"></asp:LinkButton>
            </div>
        </div>
        
        <div class="navigation">
            <ul class="menu">
                <li><a href="controls/product/ProductList.aspx">商品管理</a></li>
                <li><a href="controls/stock/StockList.aspx">在庫管理</a></li>
                <li><a href="controls/transaction/TransactionMenu.aspx">入出庫管理</a></li>
                <li><a href="controls/report/ReportMenu.aspx">レポート</a></li>
                <li><a href="controls/system/SystemMenu.aspx">システム管理</a></li>
            </ul>
        </div>
        
        <div class="content">
            <cc2:ModuleFrame runat="server" ID="ModuleFrame1" />
        </div>
        
        <div class="footer">
            <p>&copy; 2024 在庫管理システム - Powered by Asphalt Framework</p>
        </div>
    </form>
</body>
</html>
