<%@ Page Language="VB" Inherits="Dbs.Asphalt.UI.PageBase" %>
    <%@ Register Assembly="Dbs.Asphalt.Core" Namespace="Dbs.Asphalt.Core" TagPrefix="cc1" %>
        <%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc2" %>

            <!DOCTYPE html>
            <html xmlns="http://www.w3.org/1999/xhtml">

            <head runat="server">
                <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
                <title>在庫管理システム</title>
                <link href="css/default.css" rel="stylesheet" type="text/css" />
            </head>

            <body>
                <form id="form1" runat="server">
                    <div class="header">
                        <h1>在庫管理システム</h1>
                        <div class="user-info">
                            <asp:Label ID="lblUserInfo" runat="server"></asp:Label>
                            <asp:LinkButton ID="lnkLogout" runat="server" Text="ログアウト" OnClick="lnkLogout_Click">
                            </asp:LinkButton>
                        </div>
                    </div>

                    <div class="navigation">
                        <ul class="menu">
                            <li><a href="controls/product/ProductList.aspx">商品管理</a></li>
                            <li><a href="controls/stock/StockList.aspx">在庫管理</a></li>
                            <li><a href="controls/transaction/TransactionMenu.aspx">入出庫管理</a></li>
                            <li><a href="controls/report/ReportMenu.aspx">レポート</a></li>
                            <li><a href="controls/system/SystemMenu.aspx">システム管理</a></li>
                        </ul>
                    </div>

                    <div class="content">
                        <!-- ダッシュボード統計カード -->
                        <div class="dashboard-stats">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="stat-card">
                                        <div class="stat-icon">📦</div>
                                        <div class="stat-content">
                                            <div class="stat-number">
                                                <asp:Label ID="lblTotalProducts" runat="server" Text="0"></asp:Label>
                                            </div>
                                            <div class="stat-label">総商品数</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card alert-warning">
                                        <div class="stat-icon">⚠️</div>
                                        <div class="stat-content">
                                            <div class="stat-number">
                                                <asp:Label ID="lblLowStockProducts" runat="server" Text="0"></asp:Label>
                                            </div>
                                            <div class="stat-label">在庫不足</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card alert-info">
                                        <div class="stat-icon">📥</div>
                                        <div class="stat-content">
                                            <div class="stat-number">
                                                <asp:Label ID="lblTodayReceipts" runat="server" Text="0"></asp:Label>
                                            </div>
                                            <div class="stat-label">本日入庫</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card alert-success">
                                        <div class="stat-icon">📤</div>
                                        <div class="stat-content">
                                            <div class="stat-number">
                                                <asp:Label ID="lblTodayIssues" runat="server" Text="0"></asp:Label>
                                            </div>
                                            <div class="stat-label">本日出庫</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ダッシュボードメインコンテンツ -->
                        <div class="dashboard-main">
                            <div class="row">
                                <!-- 最近の取引 -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h4>📋 最近の入出庫</h4>
                                        </div>
                                        <div class="card-body">
                                            <asp:GridView ID="gvRecentTransactions" runat="server"
                                                CssClass="table table-sm" AutoGenerateColumns="false" ShowHeader="true"
                                                EmptyDataText="最近の取引データがありません。">
                                                <Columns>
                                                    <asp:BoundField DataField="TXN_DATE" HeaderText="日付"
                                                        DataFormatString="{0:MM/dd}" />
                                                    <asp:BoundField DataField="TXN_TYPE" HeaderText="種別" />
                                                    <asp:BoundField DataField="PRODUCT_NAME" HeaderText="商品名" />
                                                    <asp:BoundField DataField="QUANTITY" HeaderText="数量"
                                                        DataFormatString="{0:N0}" ItemStyle-HorizontalAlign="Right" />
                                                </Columns>
                                            </asp:GridView>
                                        </div>
                                    </div>
                                </div>

                                <!-- 在庫アラート -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h4>🚨 在庫アラート</h4>
                                        </div>
                                        <div class="card-body">
                                            <asp:GridView ID="gvStockAlerts" runat="server" CssClass="table table-sm"
                                                AutoGenerateColumns="false" ShowHeader="true"
                                                EmptyDataText="在庫アラートはありません。">
                                                <Columns>
                                                    <asp:BoundField DataField="PRODUCT_NAME" HeaderText="商品名" />
                                                    <asp:BoundField DataField="AVAILABLE_STOCK" HeaderText="利用可能"
                                                        DataFormatString="{0:N0}" ItemStyle-HorizontalAlign="Right" />
                                                    <asp:BoundField DataField="MIN_STOCK" HeaderText="最小在庫"
                                                        DataFormatString="{0:N0}" ItemStyle-HorizontalAlign="Right" />
                                                    <asp:BoundField DataField="SHORTAGE_QTY" HeaderText="不足数"
                                                        DataFormatString="{0:N0}" ItemStyle-HorizontalAlign="Right" />
                                                </Columns>
                                            </asp:GridView>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- カテゴリ別統計とトップ商品 -->
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h4>📊 カテゴリ別在庫統計</h4>
                                        </div>
                                        <div class="card-body">
                                            <asp:GridView ID="gvCategoryStats" runat="server" CssClass="table table-sm"
                                                AutoGenerateColumns="false" ShowHeader="true"
                                                EmptyDataText="カテゴリ統計データがありません。">
                                                <Columns>
                                                    <asp:BoundField DataField="CATEGORY_NAME" HeaderText="カテゴリ" />
                                                    <asp:BoundField DataField="PRODUCT_COUNT" HeaderText="商品数"
                                                        DataFormatString="{0:N0}" ItemStyle-HorizontalAlign="Right" />
                                                    <asp:BoundField DataField="TOTAL_STOCK" HeaderText="総在庫"
                                                        DataFormatString="{0:N0}" ItemStyle-HorizontalAlign="Right" />
                                                    <asp:BoundField DataField="LOW_STOCK_COUNT" HeaderText="不足商品"
                                                        DataFormatString="{0:N0}" ItemStyle-HorizontalAlign="Right" />
                                                </Columns>
                                            </asp:GridView>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h4>🏆 人気商品 TOP5</h4>
                                        </div>
                                        <div class="card-body">
                                            <asp:GridView ID="gvTopProducts" runat="server" CssClass="table table-sm"
                                                AutoGenerateColumns="false" ShowHeader="true"
                                                EmptyDataText="人気商品データがありません。">
                                                <Columns>
                                                    <asp:BoundField DataField="PRODUCT_NAME" HeaderText="商品名" />
                                                    <asp:BoundField DataField="TOTAL_ISSUE_QTY" HeaderText="出庫数"
                                                        DataFormatString="{0:N0}" ItemStyle-HorizontalAlign="Right" />
                                                    <asp:BoundField DataField="AVAILABLE_STOCK" HeaderText="在庫"
                                                        DataFormatString="{0:N0}" ItemStyle-HorizontalAlign="Right" />
                                                </Columns>
                                            </asp:GridView>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="footer">
                        <p>&copy; 2024 在庫管理システム - Powered by Asphalt Framework</p>
                    </div>
                </form>
            </body>

            </html>