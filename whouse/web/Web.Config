<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="DBSettings" type="System.Configuration.NameValueSectionHandler" />
  </configSections>
  <DBSettings>
    <add key="ConnectionString" value="Server=localhost\SQLEXPRESS;Database=WhouseDB;Integrated Security=True;Encrypt=False;" />
  </DBSettings>
  <system.web>
    <sessionState mode="SQLServer" sqlConnectionString="data source=localhost\SQLEXPRESS;Integrated Security=True" cookieless="UseDeviceProfile" timeout="1440" />
    <compilation debug="true" targetFramework="4.8">
      <assemblies>
        <add assembly="System, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Data, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Web, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Xml, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Xml.Linq, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Data.DataSetExtensions, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Web.ApplicationServices, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.ComponentModel.DataAnnotations, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Core, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Web.Services, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.EnterpriseServices, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Web.DynamicData, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Web.Entity, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Web.Extensions.Design, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
      </assemblies>
    </compilation>
    <httpRuntime maxRequestLength="100000" executionTimeout="1800" requestValidationMode="2.0" targetFramework="4.8" />
    <pages validateRequest="false" enableEventValidation="false" controlRenderingCompatibilityVersion="3.5" clientIDMode="AutoID">
      <controls>
        <add tagPrefix="ajaxToolkit" namespace="AjaxControlToolkit" assembly="AjaxControlToolkit" />
        <add tagPrefix="asphaltWebUI" namespace="Dbs.Asphalt.UI" assembly="Dbs.Asphalt.UI" />
      </controls>
    </pages>
  </system.web>
  <system.webServer>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="1073741824" />
      </requestFiltering>
    </security>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
