import os
import re
import chromadb
from chromadb.utils import embedding_functions
import pandas as pd

# === ChromaDB 初期化 ===
client = chromadb.PersistentClient(path="./chroma_db")

embedding_fn = embedding_functions.SentenceTransformerEmbeddingFunction(
    model_name="sentence-transformers/all-mpnet-base-v2"
)

collection = client.get_or_create_collection(
    name="aspnet_codebase",
    embedding_function=embedding_fn
)

# === ヘルパー関数 ===
def split_code_blocks(code, lang, file_path):
    """
    VB, ASPX, ASCX ファイルをメソッドや制御単位に分割
    """
    blocks = []

    if lang == "vb":
        # Function / Sub 単位で分割
        pattern = r"(Public |Private |Protected |Friend )?(Sub|Function)\s+[\s\S]*?(?=End (Sub|Function))End (Sub|Function)"
        matches = re.findall(pattern, code, re.MULTILINE)

        if matches:
            for m in re.finditer(pattern, code, re.MULTILINE):
                blocks.append(m.group(0))
        else:
            blocks = [code]  # 分割できなければ全体を保存

    elif lang in ("aspx", "ascx"):
        # runat="server" の VB コードを抽出
        server_code_pattern = r"<script runat=\"server\">([\s\S]*?)</script>"
        matches = re.findall(server_code_pattern, code, re.MULTILINE | re.IGNORECASE)

        for m in matches:
            blocks.append(m.strip())

        # HTML部分はそのまま保持
        html_cleaned = re.sub(server_code_pattern, "", code, flags=re.MULTILINE | re.IGNORECASE)
        if html_cleaned.strip():
            blocks.append(html_cleaned.strip())

    else:
        blocks = [code]

    return blocks


def process_file(file_path):
    """
    ファイルごとに Chroma に保存
    """
    ext = os.path.splitext(file_path)[1].lower()
    base_name = os.path.basename(file_path)

    with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
        content = f.read()

    if ext in [".ascx", ".aspx"]:
        blocks = split_code_blocks(content, "aspx", file_path)

        for i, block in enumerate(blocks):
            collection.add(
                documents=[block],
                metadatas=[{"file": file_path, "part": i, "lang": "aspx"}],
                ids=[f"{file_path}::aspx::{i}"]
            )

    elif ext in [".vb"] or base_name.endswith(".ascx.vb"):
        blocks = split_code_blocks(content, "vb", file_path)

        for i, block in enumerate(blocks):
            collection.add(
                documents=[block],
                metadatas=[{"file": file_path, "part": i, "lang": "vb"}],
                ids=[f"{file_path}::vb::{i}"]
            )
    elif ext in [".xlsx"]:
        xls = pd.ExcelFile(file_path)
        for sheet_name in xls.sheet_names:
            df = pd.read_excel(xls, sheet_name=sheet_name)

            # 行ごとに文字列化
            for _, row in df.iterrows():
                text = " | ".join([f"{col}: {row[col]}" for col in df.columns])
                # Chroma に追加
                collection.add(
                    documents=[text],
                    metadatas=[{"sheet": sheet_name, "source": file_path}],
                    ids=[f"{sheet_name}-{_}"]
                )
    else:
        print(f"Skip: {file_path}")


def index_project(root_dir):
    """
    プロジェクト全体を走査
    """
    for subdir, _, files in os.walk(root_dir):
        for file in files:
            if file.endswith((".ascx", ".ascx.vb", ".vb", ".aspx", ".xlsx")):
                file_path = os.path.join(subdir, file)
                print(f"Indexing: {file_path}")
                process_file(file_path)


# === 実行例 ===
if __name__ == "__main__":
    project_root = "../seminas.runtime-main/"  # ← ASP.NET プロジェクトのルートに変更
    #project_root = "../WarehouseSystem/"  # ← ASP.NET プロジェクトのルートに変更
    #project_root = "../../"  # ← ASP.NET プロジェクトのルートに変更
    index_project(project_root)

    print("✅ Indexing completed!")