import os
from slack_bolt import App
import chromadb
from chromadb.utils import embedding_functions
import requests
from dotenv import load_dotenv
from slack_bolt.adapter.socket_mode import SocketModeHandler

load_dotenv()  # .env ファイルを読み込む

# === Slack 設定 ===
app = App(
    token=os.environ["SLACK_BOT_TOKEN"],
    signing_secret=os.environ["SLACK_SIGNING_SECRET"]
)

# === Chroma 設定 ===
client = chromadb.PersistentClient(path="./chroma_db")
embedding_func = embedding_functions.SentenceTransformerEmbeddingFunction(
    model_name="sentence-transformers/all-mpnet-base-v2"
)
collection = client.get_or_create_collection(
    name="aspnet_codebase",
    embedding_function=embedding_func
)

# === Ollama 呼び出し ===
def ask_ollama(query, context, model="deepseek-r1:8b", stream=False):
    """
    Chroma検索結果を元に Ollama に質問し、安全に回答を取得する。
    
    query   : ユーザーの質問文字列
    context : Chroma検索結果（文字列）
    model   : 使用モデル
    stream  : Trueなら stream モードで逐次取得
    """
    prompt = f"""
あなたは社内開発支援 AsphaltというDotNetフレームワークの専門のAI エージェントです。
以下のコンテキストを参考に質問に回答してください。
日本語で回答しなさい。

過去のプロジェクト情報（コード、Excel定義書、設計資料など）を参照して
ユーザーの質問に答えます。

- 回答は事実に基づき、必要以上の推測はしない
- 検索結果以外の情報は出さない
- 複数の候補がある場合は整理して提示
- コードやテーブル定義がある場合は、具体的に書く
- ユーザーが理解しやすい形式で簡潔にまとめる
- 不明な場合は「わかりません」と正直に答える

質問: {query}

参考情報:
{context}
"""

    url = "http://localhost:11434/api/generate"
    payload = {
        "model": model,
        "prompt": prompt,
        "stream": stream
    }

    try:
        if stream:
            # streamモード対応
            res = requests.post(url, json=payload, stream=True)
            text = ""
            for line in res.iter_lines():
                if line:
                    try:
                        j = json.loads(line.decode("utf-8"))
                        text += j.get("response", "")
                    except json.JSONDecodeError:
                        # 無視して続行
                        continue
            if text.strip() == "":
                text = "Ollama の応答が取得できませんでした。"
            return text

        else:
            # 通常モード
            res = requests.post(url, json=payload, stream=False)
            try:
                data = res.json()
                return data.get("response", "Ollama の応答が取得できませんでした。")
            except json.JSONDecodeError:
                # デバッグ用に生レスポンスを出力
                print("Raw response from Ollama:", res.text)
                return "Ollama の応答が JSON として解釈できませんでした。"

    except requests.exceptions.RequestException as e:
        print("Ollama request error:", e)
        return "Ollama へのリクエストでエラーが発生しました。"

# === Slack イベントハンドラ ===
@app.event("app_mention")
def handle_mentions(body, say, ack):
    ack()

    user_text = body["event"]["text"]

    say("質問を受け取りました！少々お待ちください。処理中です…")

    try:
        # Chroma検索
        results = collection.query(query_texts=[user_text], n_results=3)
        docs = "\n\n".join([r for r in results["documents"][0]])

        # Ollama呼び出し
        answer = ask_ollama(user_text, docs)

    except Exception as e:
        print("Error in handle_mentions:", e)
        answer = "処理中にエラーが発生しました。"


    # Step 3: Slack に返信
    say(answer)

# === 起動 ===
if __name__ == "__main__":
    #app.start(port=3000)

    handler = SocketModeHandler(app, os.environ["SLACK_APP_TOKEN"])
    handler.start()